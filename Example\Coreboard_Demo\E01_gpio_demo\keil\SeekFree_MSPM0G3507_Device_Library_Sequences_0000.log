/*
 *  CMSIS Pack Debug Access Sequence Log
 *  File        : C:\Users\<USER>\Desktop\MSPM0G3507_Library-master\Example\Coreboard_Demo\E01_gpio_demo\keil\SeekFree_MSPM0G3507_Device_Library_Sequences_0000.log
 *  Created     : 12:02:53 (01/08/2025)
 *  Device      : MSPM0G3507
 *  PDSC File   : C:/keil5/TexasInstruments/MSPM0G1X0X_G3X0X_DFP/1.3.1_tmp/TexasInstruments.MSPM0G1X0X_G3X0X_DFP.pdsc
 *
 */

[12:02:53.940]  **********  Sequence "DebugPortSetup"  (Context="Connect", Pname="", info="")
[12:02:53.940]  
[12:02:53.940]  <debugvars>
[12:02:53.940]    // Pre-defined
[12:02:53.940]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[12:02:53.941]    __connection=0x00000201    (Connection Type="Debug", Reset Type="System Reset")
[12:02:53.941]    __dp=0x00000000
[12:02:53.941]    __ap=0x00000000
[12:02:53.941]    __traceout=0x00000000      (Trace Disabled)
[12:02:53.941]    __errorcontrol=0x00000000  (Skip Errors="False")
[12:02:53.941]    __FlashAddr=0x00000000
[12:02:53.941]    __FlashLen=0x00000000
[12:02:53.941]    __FlashArg=0x00000000
[12:02:53.941]    __FlashOp=0x00000000
[12:02:53.941]    __Result=0x00000000
[12:02:53.941]  </debugvars>
[12:02:53.941]  
[12:02:53.941]  <sequence name="DebugPortSetup" Pname="" disable="false" info="">
[12:02:53.941]    <block atomic="false" info="">
[12:02:53.941]      __var isSWJ      = ((__protocol & 0x00010000) != 0);
[12:02:53.942]        // -> [isSWJ <= 0x00000001]
[12:02:53.942]      __var hasDormant = __protocol & 0x00020000;
[12:02:53.942]        // -> [hasDormant <= 0x00000000]
[12:02:53.942]      __var protType   = __protocol & 0x0000FFFF;
[12:02:53.942]        // -> [protType <= 0x00000002]
[12:02:53.942]    </block>
[12:02:53.942]    <control if="protType == 1" while="" timeout="0" info="">
[12:02:53.942]      // if-block "protType == 1"
[12:02:53.942]        // =>  FALSE
[12:02:53.942]      // skip if-block "protType == 1"
[12:02:53.942]    </control>
[12:02:53.942]    <control if="protType == 2" while="" timeout="0" info="">
[12:02:53.942]      // if-block "protType == 2"
[12:02:53.942]        // =>  TRUE
[12:02:53.942]      <control if="isSWJ" while="" timeout="0" info="">
[12:02:53.943]        // if-block "isSWJ"
[12:02:53.943]          // =>  TRUE
[12:02:53.943]        <control if="hasDormant" while="" timeout="0" info="">
[12:02:53.943]          // if-block "hasDormant"
[12:02:53.943]            // =>  FALSE
[12:02:53.943]          // skip if-block "hasDormant"
[12:02:53.943]        </control>
[12:02:53.943]        <control if="!hasDormant" while="" timeout="0" info="">
[12:02:53.943]          // if-block "!hasDormant"
[12:02:53.943]            // =>  TRUE
[12:02:53.943]          <block atomic="false" info="">
[12:02:53.943]            DAP_SWJ_Sequence(51, 0x0007FFFFFFFFFFFF); //lets put the JTAG line in a reset state
[12:02:53.945]              // -> [DAP_SWJ_Sequence(51, 0x0007FFFFFFFFFFFF)]
[12:02:53.946]            DAP_SWJ_Sequence(16,0xE73C); //SWD to JTAG, this is to ensure the state machine is initialized
[12:02:53.947]              // -> [DAP_SWJ_Sequence(16, 0x000000000000E73C)]
[12:02:53.947]            DAP_SWJ_Sequence(51, 0x0007FFFFFFFFFFFF); //Put it back in reset
[12:02:53.949]              // -> [DAP_SWJ_Sequence(51, 0x0007FFFFFFFFFFFF)]
[12:02:53.949]            DAP_SWJ_Sequence(16, 0xE79E); //JTAG to SWD switch
[12:02:53.951]              // -> [DAP_SWJ_Sequence(16, 0x000000000000E79E)]
[12:02:53.951]            DAP_SWJ_Sequence(51, 0x0007FFFFFFFFFFFF);  //Ensure the switch is successful 
[12:02:53.953]              // -> [DAP_SWJ_Sequence(51, 0x0007FFFFFFFFFFFF)]
[12:02:53.953]            DAP_SWJ_Sequence(7,  0x00); //Give the line some time to initialize
[12:02:53.955]              // -> [DAP_SWJ_Sequence(7, 0x0000000000000000)]
[12:02:53.955]          </block>
[12:02:53.955]          // end if-block "!hasDormant"
[12:02:53.955]        </control>
[12:02:53.955]        // end if-block "isSWJ"
[12:02:53.955]      </control>
[12:02:53.955]      <control if="!isSWJ" while="" timeout="0" info="">
[12:02:53.956]        // if-block "!isSWJ"
[12:02:53.956]          // =>  FALSE
[12:02:53.956]        // skip if-block "!isSWJ"
[12:02:53.956]      </control>
[12:02:53.956]      <block atomic="false" info="">
[12:02:53.956]        ReadDP(0x0);
[12:02:53.958]          // -> [ReadDP(0x00000000) => 0x6BA02477]   (__dp=0x00000000)
[12:02:53.958]      </block>
[12:02:53.958]      // end if-block "protType == 2"
[12:02:53.958]    </control>
[12:02:53.958]  </sequence>
[12:02:53.958]  
[12:03:01.458]  **********  Sequence "DebugPortSetup"  (Context="Connect", Pname="", info="")
[12:03:01.458]  
[12:03:01.458]  <debugvars>
[12:03:01.459]    // Pre-defined
[12:03:01.459]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[12:03:01.459]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[12:03:01.459]    __dp=0x00000000
[12:03:01.459]    __ap=0x00000000
[12:03:01.459]    __traceout=0x00000000      (Trace Disabled)
[12:03:01.459]    __errorcontrol=0x00000000  (Skip Errors="False")
[12:03:01.460]    __FlashAddr=0x00000000
[12:03:01.460]    __FlashLen=0x00000000
[12:03:01.460]    __FlashArg=0x00000000
[12:03:01.460]    __FlashOp=0x00000000
[12:03:01.460]    __Result=0x00000000
[12:03:01.461]  </debugvars>
[12:03:01.461]  
[12:03:01.461]  <sequence name="DebugPortSetup" Pname="" disable="false" info="">
[12:03:01.461]    <block atomic="false" info="">
[12:03:01.461]      __var isSWJ      = ((__protocol & 0x00010000) != 0);
[12:03:01.461]        // -> [isSWJ <= 0x00000001]
[12:03:01.461]      __var hasDormant = __protocol & 0x00020000;
[12:03:01.461]        // -> [hasDormant <= 0x00000000]
[12:03:01.461]      __var protType   = __protocol & 0x0000FFFF;
[12:03:01.462]        // -> [protType <= 0x00000002]
[12:03:01.462]    </block>
[12:03:01.462]    <control if="protType == 1" while="" timeout="0" info="">
[12:03:01.462]      // if-block "protType == 1"
[12:03:01.462]        // =>  FALSE
[12:03:01.462]      // skip if-block "protType == 1"
[12:03:01.462]    </control>
[12:03:01.462]    <control if="protType == 2" while="" timeout="0" info="">
[12:03:01.462]      // if-block "protType == 2"
[12:03:01.462]        // =>  TRUE
[12:03:01.463]      <control if="isSWJ" while="" timeout="0" info="">
[12:03:01.463]        // if-block "isSWJ"
[12:03:01.463]          // =>  TRUE
[12:03:01.463]        <control if="hasDormant" while="" timeout="0" info="">
[12:03:01.463]          // if-block "hasDormant"
[12:03:01.463]            // =>  FALSE
[12:03:01.463]          // skip if-block "hasDormant"
[12:03:01.463]        </control>
[12:03:01.463]        <control if="!hasDormant" while="" timeout="0" info="">
[12:03:01.463]          // if-block "!hasDormant"
[12:03:01.463]            // =>  TRUE
[12:03:01.463]          <block atomic="false" info="">
[12:03:01.463]            DAP_SWJ_Sequence(51, 0x0007FFFFFFFFFFFF); //lets put the JTAG line in a reset state
[12:03:01.465]              // -> [DAP_SWJ_Sequence(51, 0x0007FFFFFFFFFFFF)]
[12:03:01.465]            DAP_SWJ_Sequence(16,0xE73C); //SWD to JTAG, this is to ensure the state machine is initialized
[12:03:01.468]              // -> [DAP_SWJ_Sequence(16, 0x000000000000E73C)]
[12:03:01.468]            DAP_SWJ_Sequence(51, 0x0007FFFFFFFFFFFF); //Put it back in reset
[12:03:01.470]              // -> [DAP_SWJ_Sequence(51, 0x0007FFFFFFFFFFFF)]
[12:03:01.470]            DAP_SWJ_Sequence(16, 0xE79E); //JTAG to SWD switch
[12:03:01.472]              // -> [DAP_SWJ_Sequence(16, 0x000000000000E79E)]
[12:03:01.472]            DAP_SWJ_Sequence(51, 0x0007FFFFFFFFFFFF);  //Ensure the switch is successful 
[12:03:01.474]              // -> [DAP_SWJ_Sequence(51, 0x0007FFFFFFFFFFFF)]
[12:03:01.474]            DAP_SWJ_Sequence(7,  0x00); //Give the line some time to initialize
[12:03:01.476]              // -> [DAP_SWJ_Sequence(7, 0x0000000000000000)]
[12:03:01.476]          </block>
[12:03:01.476]          // end if-block "!hasDormant"
[12:03:01.476]        </control>
[12:03:01.476]        // end if-block "isSWJ"
[12:03:01.476]      </control>
[12:03:01.476]      <control if="!isSWJ" while="" timeout="0" info="">
[12:03:01.476]        // if-block "!isSWJ"
[12:03:01.477]          // =>  FALSE
[12:03:01.477]        // skip if-block "!isSWJ"
[12:03:01.477]      </control>
[12:03:01.477]      <block atomic="false" info="">
[12:03:01.477]        ReadDP(0x0);
[12:03:01.479]          // -> [ReadDP(0x00000000) => 0x6BA02477]   (__dp=0x00000000)
[12:03:01.479]      </block>
[12:03:01.479]      // end if-block "protType == 2"
[12:03:01.479]    </control>
[12:03:01.479]  </sequence>
[12:03:01.479]  
[12:03:01.482]  **********  Sequence "DebugPortStart"  (Context="Connect", Pname="", info="")
[12:03:01.482]  
[12:03:01.483]  <debugvars>
[12:03:01.483]    // Pre-defined
[12:03:01.483]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[12:03:01.483]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[12:03:01.483]    __dp=0x00000000
[12:03:01.483]    __ap=0x00000000
[12:03:01.483]    __traceout=0x00000000      (Trace Disabled)
[12:03:01.483]    __errorcontrol=0x00000000  (Skip Errors="False")
[12:03:01.483]    __FlashAddr=0x00000000
[12:03:01.483]    __FlashLen=0x00000000
[12:03:01.483]    __FlashArg=0x00000000
[12:03:01.483]    __FlashOp=0x00000000
[12:03:01.484]    __Result=0x00000000
[12:03:01.484]  </debugvars>
[12:03:01.484]  
[12:03:01.484]  <sequence name="DebugPortStart" Pname="" disable="false" info="">
[12:03:01.484]    <block atomic="false" info="">
[12:03:01.484]      __var SW_DP_ABORT       = 0x0;
[12:03:01.484]        // -> [SW_DP_ABORT <= 0x00000000]
[12:03:01.484]      __var DP_CTRL_STAT      = 0x4;
[12:03:01.484]        // -> [DP_CTRL_STAT <= 0x00000004]
[12:03:01.484]      __var DP_SELECT         = 0x8;
[12:03:01.484]        // -> [DP_SELECT <= 0x00000008]
[12:03:01.484]      __var powered_down      = 0;
[12:03:01.484]        // -> [powered_down <= 0x00000000]
[12:03:01.485]      WriteDP(DP_SELECT, 0x00000000);
[12:03:01.486]        // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[12:03:01.487]      powered_down = ((ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000);
[12:03:01.488]        // -> [ReadDP(0x00000004) => 0x00000040]   (__dp=0x00000000)
[12:03:01.488]        // -> [powered_down <= 0x00000001]
[12:03:01.488]    </block>
[12:03:01.488]    <control if="powered_down" while="" timeout="0" info="">
[12:03:01.490]      // if-block "powered_down"
[12:03:01.490]        // =>  TRUE
[12:03:01.490]      <block atomic="false" info="">
[12:03:01.490]        Message(0, "Debug/System power-up request sent");
[12:03:01.491]        WriteDP(DP_CTRL_STAT, 0x50000000);
[12:03:01.493]          // -> [WriteDP(0x00000004, 0x50000000)]   (__dp=0x00000000)
[12:03:01.493]      </block>
[12:03:01.493]      <control if="" while="(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000" timeout="1000000" info="">
[12:03:01.493]        // while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"  (timeout="1000000")
[12:03:01.497]          // -> [ReadDP(0x00000004) => 0xF0000040]   (__dp=0x00000000)
[12:03:01.497]        // while-condition  =>  FALSE
[12:03:01.497]        // end while "(ReadDP(DP_CTRL_STAT) & 0xA0000000) != 0xA0000000"
[12:03:01.497]      </control>
[12:03:01.498]      <control if="(__protocol & 0xFFFF) == 1" while="" timeout="0" info="">
[12:03:01.498]        // if-block "(__protocol & 0xFFFF) == 1"
[12:03:01.498]          // =>  FALSE
[12:03:01.498]        // skip if-block "(__protocol & 0xFFFF) == 1"
[12:03:01.498]      </control>
[12:03:01.498]      <control if="(__protocol & 0xFFFF) == 2" while="" timeout="0" info="">
[12:03:01.498]        // if-block "(__protocol & 0xFFFF) == 2"
[12:03:01.498]          // =>  TRUE
[12:03:01.498]        <block atomic="false" info="">
[12:03:01.498]          Message(0, "executing SWD power up");
[12:03:01.499]          WriteDP(DP_CTRL_STAT, 0x50000F00);
[12:03:01.501]            // -> [WriteDP(0x00000004, 0x50000F00)]   (__dp=0x00000000)
[12:03:01.501]          WriteDP(SW_DP_ABORT, 0x0000001E);
[12:03:01.503]            // -> [WriteDP(0x00000000, 0x0000001E)]   (__dp=0x00000000)
[12:03:01.503]        </block>
[12:03:01.503]        // end if-block "(__protocol & 0xFFFF) == 2"
[12:03:01.503]      </control>
[12:03:01.503]      // end if-block "powered_down"
[12:03:01.503]    </control>
[12:03:01.503]    <block atomic="false" info="">
[12:03:01.503]      __var DEBUG_PORT_VAL    = 0;
[12:03:01.503]        // -> [DEBUG_PORT_VAL <= 0x00000000]
[12:03:01.503]      __var ACCESS_POINT_VAL  = 0;
[12:03:01.503]        // -> [ACCESS_POINT_VAL <= 0x00000000]
[12:03:01.503]      __ap = 1; 
[12:03:01.503]        // -> [__ap <= 0x00000001]
[12:03:01.504]      WriteAP(0x0C, 0x04); //lets use the mini pwr-ap as backup
[12:03:01.507]        // -> [WriteAP(0x0000000C, 0x00000004)]   (__dp=0x00000000, __ap=0x00000001)
[12:03:01.507]      __ap = 4;
[12:03:01.507]        // -> [__ap <= 0x00000004]
[12:03:01.507]      ACCESS_POINT_VAL = ReadAP(0x00);//Reading current state of access point
[12:03:01.512]        // -> [ReadAP(0x00000000) => 0x00080027]   (__dp=0x00000000, __ap=0x00000004)
[12:03:01.512]        // -> [ACCESS_POINT_VAL <= 0x00080027]
[12:03:01.512]      Message(0, "Current state of access point is: %x",ACCESS_POINT_VAL);
[12:03:01.513]    </block>
[12:03:01.513]    <block atomic="false" info="">
[12:03:01.514]      __var nReset = 0x80;
[12:03:01.514]        // -> [nReset <= 0x00000080]
[12:03:01.514]      __var canReadPins = 0;
[12:03:01.514]        // -> [canReadPins <= 0x00000000]
[12:03:01.514]      canReadPins = (DAP_SWJ_Pins(0x00, nReset, 0) != 0xFFFFFFFF);
[12:03:01.516]        // -> [DAP_SWJ_Pins(0x00, 0x80, 0) => 0x02]   (Out: nRESET=0  =>  In: SWCLK/TCK=0, SWDIO/TMS=1, TDI=0, TDO=0, nTRST=0, nRESET=0)
[12:03:01.516]        // -> [canReadPins <= 0x00000001]
[12:03:01.516]    </block>
[12:03:01.516]    <control if="" while="1" timeout="200" info="">
[12:03:01.516]      // while "1"  (timeout="200")
[12:03:01.516]      // while-condition  =>  TRUE
[12:03:01.516]      // while "1"  (timeout="200")
[12:03:01.516]      // while-condition  =>  TRUE
[12:03:01.517]      // while "1"  (timeout="200")
[12:03:01.517]      // while-condition  =>  TRUE
[12:03:01.517]      // while "1"  (timeout="200")
[12:03:01.517]      // while-condition  =>  TRUE
[12:03:01.517]      // while "1"  (timeout="200")
[12:03:01.517]      // while-condition  =>  TRUE
[12:03:01.517]      // while "1"  (timeout="200")
[12:03:01.517]      // while-condition  =>  TRUE
[12:03:01.517]      // while "1"  (timeout="200")
[12:03:01.517]      // while-condition  =>  TRUE
[12:03:01.517]      // while "1"  (timeout="200")
[12:03:01.517]      // while-condition  =>  TRUE
[12:03:01.517]      // while "1"  (timeout="200")
[12:03:01.517]      // while-condition  =>  TRUE
[12:03:01.517]      // while "1"  (timeout="200")
[12:03:01.518]      // while-condition  =>  TRUE
[12:03:01.518]      // while "1"  (timeout="200")
[12:03:01.518]      // while-condition  =>  TRUE
[12:03:01.518]      // while "1"  (timeout="200")
[12:03:01.518]      // while-condition  =>  TRUE
[12:03:01.518]      // while "1"  (timeout="200")
[12:03:01.518]      // while-condition  =>  TRUE
[12:03:01.518]      // while "1"  (timeout="200")
[12:03:01.518]      // while-condition  =>  TRUE
[12:03:01.518]      // while "1"  (timeout="200")
[12:03:01.518]      // while-condition  =>  TRUE
[12:03:01.518]      // while "1"  (timeout="200")
[12:03:01.518]      // while-condition  =>  TRUE
[12:03:01.518]      // while "1"  (timeout="200")
[12:03:01.519]      // while-condition  =>  TRUE
[12:03:01.519]      // while "1"  (timeout="200")
[12:03:01.519]      // while-condition  =>  TRUE
[12:03:01.519]      // while "1"  (timeout="200")
[12:03:01.519]      // while-condition  =>  TRUE
[12:03:01.519]      // while "1"  (timeout="200")
[12:03:01.519]      // while-condition  =>  TRUE
[12:03:01.519]      // while "1"  (timeout="200")
[12:03:01.519]      // while-condition  =>  TRUE
[12:03:01.519]      // while "1"  (timeout="200")
[12:03:01.519]      // while-condition  =>  TRUE
[12:03:01.519]      // while "1"  (timeout="200")
[12:03:01.519]      // while-condition  =>  TRUE
[12:03:01.519]      // while "1"  (timeout="200")
[12:03:01.519]      // while-condition  =>  TRUE
[12:03:01.520]      // while "1"  (timeout="200")
[12:03:01.520]      // while-condition  =>  TRUE
[12:03:01.520]      // while "1"  (timeout="200")
[12:03:01.520]      // while-condition  =>  TRUE
[12:03:01.520]      // while "1"  (timeout="200")
[12:03:01.520]      // while-condition  =>  TRUE
[12:03:01.520]      // while "1"  (timeout="200")
[12:03:01.520]      // while-condition  =>  TRUE
[12:03:01.520]      // while "1"  (timeout="200")
[12:03:01.520]      // while-condition  =>  TRUE
[12:03:01.520]      // while "1"  (timeout="200")
[12:03:01.520]      // while-condition  =>  TRUE
[12:03:01.520]      // while "1"  (timeout="200")
[12:03:01.521]      // while-condition  =>  TRUE
[12:03:01.521]      // while "1"  (timeout="200")
[12:03:01.521]      // while-condition  =>  TRUE
[12:03:01.521]      // while "1"  (timeout="200")
[12:03:01.521]      // while-condition  =>  TRUE
[12:03:01.521]      // while "1"  (timeout="200")
[12:03:01.521]      // while-condition  =>  TRUE
[12:03:01.521]      // while "1"  (timeout="200")
[12:03:01.521]      // while-condition  =>  TRUE
[12:03:01.521]      // while "1"  (timeout="200")
[12:03:01.521]      // while-condition  =>  TRUE
[12:03:01.521]      // while "1"  (timeout="200")
[12:03:01.521]      // while-condition  =>  TRUE
[12:03:01.521]      // while "1"  (timeout="200")
[12:03:01.521]      // while-condition  =>  TRUE
[12:03:01.522]      // while "1"  (timeout="200")
[12:03:01.522]      // while  =>  TIMEOUT
[12:03:01.522]      // end while "1"
[12:03:01.522]    </control>
[12:03:01.522]    <control if="canReadPins" while="" timeout="0" info="">
[12:03:01.522]      // if-block "canReadPins"
[12:03:01.522]        // =>  TRUE
[12:03:01.522]      <control if="" while="(DAP_SWJ_Pins(nReset, nReset, 0) & nReset) == 0" timeout="1000000" info="">
[12:03:01.522]        // while "(DAP_SWJ_Pins(nReset, nReset, 0) & nReset) == 0"  (timeout="1000000")
[12:03:01.524]          // -> [DAP_SWJ_Pins(0x80, 0x80, 0) => 0x02]   (Out: nRESET=1  =>  In: SWCLK/TCK=0, SWDIO/TMS=1, TDI=0, TDO=0, nTRST=0, nRESET=0)
[12:03:01.524]        // while-condition  =>  TRUE
[12:03:01.524]        // while "(DAP_SWJ_Pins(nReset, nReset, 0) & nReset) == 0"  (timeout="1000000")
[12:03:01.526]          // -> [DAP_SWJ_Pins(0x80, 0x80, 0) => 0x82]   (Out: nRESET=1  =>  In: SWCLK/TCK=0, SWDIO/TMS=1, TDI=0, TDO=0, nTRST=0, nRESET=1)
[12:03:01.526]        // while-condition  =>  FALSE
[12:03:01.526]        // end while "(DAP_SWJ_Pins(nReset, nReset, 0) & nReset) == 0"
[12:03:01.526]      </control>
[12:03:01.526]      // end if-block "canReadPins"
[12:03:01.527]    </control>
[12:03:01.527]    <control if="!canReadPins" while="" timeout="0" info="">
[12:03:01.527]      // if-block "!canReadPins"
[12:03:01.527]        // =>  FALSE
[12:03:01.527]      // skip if-block "!canReadPins"
[12:03:01.527]    </control>
[12:03:01.527]    <control if="(ACCESS_POINT_VAL & 0x00E00000) == 0" while="" timeout="0" info="">
[12:03:01.527]      // if-block "(ACCESS_POINT_VAL & 0x00E00000) == 0"
[12:03:01.527]        // =>  TRUE
[12:03:01.527]      <block atomic="false" info="">
[12:03:01.527]        WriteAP(0x00, 0x190008);
[12:03:01.529]          // -> [WriteAP(0x00000000, 0x00190008)]   (__dp=0x00000000, __ap=0x00000004)
[12:03:01.529]        WriteAP(0xF0, 0x01);
[12:03:01.532]          // -> [WriteAP(0x000000F0, 0x00000001)]   (__dp=0x00000000, __ap=0x00000004)
[12:03:01.533]      </block>
[12:03:01.533]      // end if-block "(ACCESS_POINT_VAL & 0x00E00000) == 0"
[12:03:01.533]    </control>
[12:03:01.533]    <control if="(ACCESS_POINT_VAL & 0x00E00000) != 0" while="" timeout="0" info="">
[12:03:01.533]      // if-block "(ACCESS_POINT_VAL & 0x00E00000) != 0"
[12:03:01.533]        // =>  FALSE
[12:03:01.533]      // skip if-block "(ACCESS_POINT_VAL & 0x00E00000) != 0"
[12:03:01.533]    </control>
[12:03:01.533]    <block atomic="false" info="">
[12:03:01.533]      ACCESS_POINT_VAL = ReadAP(0x00);//Reading current state of access point
[12:03:01.537]        // -> [ReadAP(0x00000000) => 0x0079002F]   (__dp=0x00000000, __ap=0x00000004)
[12:03:01.537]        // -> [ACCESS_POINT_VAL <= 0x0079002F]
[12:03:01.538]      Message(0, "Current state of access point is: %x",ACCESS_POINT_VAL);
[12:03:01.544]      __ap = 0; //lets make sure we reset the access point selection
[12:03:01.544]        // -> [__ap <= 0x00000000]
[12:03:01.544]    </block>
[12:03:01.544]  </sequence>
[12:03:01.544]  
[12:03:01.564]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[12:03:01.564]  
[12:03:01.564]  <debugvars>
[12:03:01.564]    // Pre-defined
[12:03:01.565]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[12:03:01.565]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[12:03:01.565]    __dp=0x00000000
[12:03:01.565]    __ap=0x00000000
[12:03:01.565]    __traceout=0x00000000      (Trace Disabled)
[12:03:01.565]    __errorcontrol=0x00000000  (Skip Errors="False")
[12:03:01.565]    __FlashAddr=0x00000000
[12:03:01.565]    __FlashLen=0x00000000
[12:03:01.565]    __FlashArg=0x00000000
[12:03:01.565]    __FlashOp=0x00000000
[12:03:01.565]    __Result=0x00000000
[12:03:01.565]  </debugvars>
[12:03:01.566]  
[12:03:01.566]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[12:03:01.566]    <block atomic="false" info="">
[12:03:01.566]      __var deviceID = 0;
[12:03:01.566]        // -> [deviceID <= 0x00000000]
[12:03:01.566]      __var version = 0;
[12:03:01.566]        // -> [version <= 0x00000000]
[12:03:01.566]      __var partNum = 0;
[12:03:01.566]        // -> [partNum <= 0x00000000]
[12:03:01.566]      __var manuf = 0;
[12:03:01.566]        // -> [manuf <= 0x00000000]
[12:03:01.566]      __var isMSPM0G1X0X_G3X0X = 0;
[12:03:01.567]        // -> [isMSPM0G1X0X_G3X0X <= 0x00000000]
[12:03:01.567]      __var isProduction = 0;
[12:03:01.567]        // -> [isProduction <= 0x00000000]
[12:03:01.567]      __var continueId = 0;
[12:03:01.567]        // -> [continueId <= 0x00000000]
[12:03:01.567]      deviceID =   Read32(0x41C40004);
[12:03:01.573]        // -> [Read32(0x41C40004) => 0x2BB8802F]   (__dp=0x00000000, __ap=0x00000000)
[12:03:01.573]        // -> [deviceID <= 0x2BB8802F]
[12:03:01.573]      version = deviceID >> 28;
[12:03:01.573]        // -> [version <= 0x00000002]
[12:03:01.573]      partNum = (deviceID & 0x0FFFF000) >> 12;
[12:03:01.573]        // -> [partNum <= 0x0000BB88]
[12:03:01.573]      manuf = (deviceID & 0x00000FFE) >> 1;
[12:03:01.573]        // -> [manuf <= 0x00000017]
[12:03:01.573]      isMSPM0G1X0X_G3X0X = (partNum == 0xBB88) && (manuf == 0x17);
[12:03:01.574]        // -> [isMSPM0G1X0X_G3X0X <= 0x00000001]
[12:03:01.574]      isProduction = (version > 0);
[12:03:01.574]        // -> [isProduction <= 0x00000001]
[12:03:01.574]    </block>
[12:03:01.574]    <control if="!isMSPM0G1X0X_G3X0X" while="" timeout="0" info="">
[12:03:01.574]      // if-block "!isMSPM0G1X0X_G3X0X"
[12:03:01.574]        // =>  FALSE
[12:03:01.574]      // skip if-block "!isMSPM0G1X0X_G3X0X"
[12:03:01.574]    </control>
[12:03:01.574]    <control if="continueId == 4" while="" timeout="0" info="">
[12:03:01.574]      // if-block "continueId == 4"
[12:03:01.574]        // =>  FALSE
[12:03:01.574]      // skip if-block "continueId == 4"
[12:03:01.575]    </control>
[12:03:01.575]    <control if="!isProduction" while="" timeout="0" info="">
[12:03:01.575]      // if-block "!isProduction"
[12:03:01.575]        // =>  FALSE
[12:03:01.575]      // skip if-block "!isProduction"
[12:03:01.575]    </control>
[12:03:01.575]  </sequence>
[12:03:01.575]  
[12:08:53.574]  **********  Sequence "DebugPortSetup"  (Context="Connect", Pname="", info="")
[12:08:53.574]  
[12:08:53.575]  <debugvars>
[12:08:53.575]    // Pre-defined
[12:08:53.575]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[12:08:53.576]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[12:08:53.576]    __dp=0x00000000
[12:08:53.576]    __ap=0x00000000
[12:08:53.576]    __traceout=0x00000000      (Trace Disabled)
[12:08:53.576]    __errorcontrol=0x00000000  (Skip Errors="False")
[12:08:53.576]    __FlashAddr=0x00000000
[12:08:53.576]    __FlashLen=0x00000000
[12:08:53.576]    __FlashArg=0x00000000
[12:08:53.577]    __FlashOp=0x00000000
[12:08:53.577]    __Result=0x00000000
[12:08:53.577]  </debugvars>
[12:08:53.577]  
[12:08:53.577]  <sequence name="DebugPortSetup" Pname="" disable="false" info="">
[12:08:53.577]    <block atomic="false" info="">
[12:08:53.577]      __var isSWJ      = ((__protocol & 0x00010000) != 0);
[12:08:53.577]        // -> [isSWJ <= 0x00000001]
[12:08:53.577]      __var hasDormant = __protocol & 0x00020000;
[12:08:53.577]        // -> [hasDormant <= 0x00000000]
[12:08:53.577]      __var protType   = __protocol & 0x0000FFFF;
[12:08:53.577]        // -> [protType <= 0x00000002]
[12:08:53.577]    </block>
[12:08:53.577]    <control if="protType == 1" while="" timeout="0" info="">
[12:08:53.577]      // if-block "protType == 1"
[12:08:53.578]        // =>  FALSE
[12:08:53.578]      // skip if-block "protType == 1"
[12:08:53.578]    </control>
[12:08:53.578]    <control if="protType == 2" while="" timeout="0" info="">
[12:08:53.578]      // if-block "protType == 2"
[12:08:53.578]        // =>  TRUE
[12:08:53.578]      <control if="isSWJ" while="" timeout="0" info="">
[12:08:53.578]        // if-block "isSWJ"
[12:08:53.578]          // =>  TRUE
[12:08:53.578]        <control if="hasDormant" while="" timeout="0" info="">
[12:08:53.578]          // if-block "hasDormant"
[12:08:53.578]            // =>  FALSE
[12:08:53.578]          // skip if-block "hasDormant"
[12:08:53.578]        </control>
[12:08:53.579]        <control if="!hasDormant" while="" timeout="0" info="">
[12:08:53.579]          // if-block "!hasDormant"
[12:08:53.579]            // =>  TRUE
[12:08:53.579]          <block atomic="false" info="">
[12:08:53.579]            DAP_SWJ_Sequence(51, 0x0007FFFFFFFFFFFF); //lets put the JTAG line in a reset state
[12:08:53.580]              // -> [DAP_SWJ_Sequence(51, 0x0007FFFFFFFFFFFF)]
[12:08:53.581]            DAP_SWJ_Sequence(16,0xE73C); //SWD to JTAG, this is to ensure the state machine is initialized
[12:08:53.582]              // -> [DAP_SWJ_Sequence(16, 0x000000000000E73C)]
[12:08:53.582]            DAP_SWJ_Sequence(51, 0x0007FFFFFFFFFFFF); //Put it back in reset
[12:08:53.584]              // -> [DAP_SWJ_Sequence(51, 0x0007FFFFFFFFFFFF)]
[12:08:53.584]            DAP_SWJ_Sequence(16, 0xE79E); //JTAG to SWD switch
[12:08:53.587]              // -> [DAP_SWJ_Sequence(16, 0x000000000000E79E)]
[12:08:53.588]            DAP_SWJ_Sequence(51, 0x0007FFFFFFFFFFFF);  //Ensure the switch is successful 
[12:08:53.590]              // -> [DAP_SWJ_Sequence(51, 0x0007FFFFFFFFFFFF)]
[12:08:53.590]            DAP_SWJ_Sequence(7,  0x00); //Give the line some time to initialize
[12:08:53.592]              // -> [DAP_SWJ_Sequence(7, 0x0000000000000000)]
[12:08:53.592]          </block>
[12:08:53.592]          // end if-block "!hasDormant"
[12:08:53.592]        </control>
[12:08:53.592]        // end if-block "isSWJ"
[12:08:53.593]      </control>
[12:08:53.593]      <control if="!isSWJ" while="" timeout="0" info="">
[12:08:53.593]        // if-block "!isSWJ"
[12:08:53.593]          // =>  FALSE
[12:08:53.593]        // skip if-block "!isSWJ"
[12:08:53.593]      </control>
[12:08:53.593]      <block atomic="false" info="">
[12:08:53.593]        ReadDP(0x0);
[12:08:53.612]  
[12:08:53.612]  !!! E310 : Debug access failed - cannot read DP register 0x00
[12:08:53.612]  
[12:08:53.613]          // -> [ReadDP(0x00000000) => 0x6BA02477]   (__dp=0x00000000)
[12:08:53.613]      </block>
[12:08:53.613]      // end if-block "protType == 2"
[12:08:53.613]    </control>
[12:08:53.613]  </sequence>
[12:08:53.613]  
[12:08:58.645]  **********  Sequence "DebugPortSetup"  (Context="Connect", Pname="", info="")
[12:08:58.645]  
[12:08:58.646]  <debugvars>
[12:08:58.646]    // Pre-defined
[12:08:58.646]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[12:08:58.646]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[12:08:58.646]    __dp=0x00000000
[12:08:58.646]    __ap=0x00000000
[12:08:58.646]    __traceout=0x00000000      (Trace Disabled)
[12:08:58.646]    __errorcontrol=0x00000000  (Skip Errors="False")
[12:08:58.646]    __FlashAddr=0x00000000
[12:08:58.646]    __FlashLen=0x00000000
[12:08:58.646]    __FlashArg=0x00000000
[12:08:58.646]    __FlashOp=0x00000000
[12:08:58.646]    __Result=0x00000000
[12:08:58.646]  </debugvars>
[12:08:58.646]  
[12:08:58.646]  <sequence name="DebugPortSetup" Pname="" disable="false" info="">
[12:08:58.647]    <block atomic="false" info="">
[12:08:58.647]      __var isSWJ      = ((__protocol & 0x00010000) != 0);
[12:08:58.647]        // -> [isSWJ <= 0x00000001]
[12:08:58.647]      __var hasDormant = __protocol & 0x00020000;
[12:08:58.647]        // -> [hasDormant <= 0x00000000]
[12:08:58.647]      __var protType   = __protocol & 0x0000FFFF;
[12:08:58.647]        // -> [protType <= 0x00000002]
[12:08:58.647]    </block>
[12:08:58.647]    <control if="protType == 1" while="" timeout="0" info="">
[12:08:58.647]      // if-block "protType == 1"
[12:08:58.647]        // =>  FALSE
[12:08:58.647]      // skip if-block "protType == 1"
[12:08:58.647]    </control>
[12:08:58.647]    <control if="protType == 2" while="" timeout="0" info="">
[12:08:58.647]      // if-block "protType == 2"
[12:08:58.648]        // =>  TRUE
[12:08:58.648]      <control if="isSWJ" while="" timeout="0" info="">
[12:08:58.648]        // if-block "isSWJ"
[12:08:58.648]          // =>  TRUE
[12:08:58.648]        <control if="hasDormant" while="" timeout="0" info="">
[12:08:58.648]          // if-block "hasDormant"
[12:08:58.648]            // =>  FALSE
[12:08:58.648]          // skip if-block "hasDormant"
[12:08:58.648]        </control>
[12:08:58.648]        <control if="!hasDormant" while="" timeout="0" info="">
[12:08:58.648]          // if-block "!hasDormant"
[12:08:58.648]            // =>  TRUE
[12:08:58.648]          <block atomic="false" info="">
[12:08:58.648]            DAP_SWJ_Sequence(51, 0x0007FFFFFFFFFFFF); //lets put the JTAG line in a reset state
[12:08:58.650]              // -> [DAP_SWJ_Sequence(51, 0x0007FFFFFFFFFFFF)]
[12:08:58.651]            DAP_SWJ_Sequence(16,0xE73C); //SWD to JTAG, this is to ensure the state machine is initialized
[12:08:58.652]              // -> [DAP_SWJ_Sequence(16, 0x000000000000E73C)]
[12:08:58.653]            DAP_SWJ_Sequence(51, 0x0007FFFFFFFFFFFF); //Put it back in reset
[12:08:58.654]              // -> [DAP_SWJ_Sequence(51, 0x0007FFFFFFFFFFFF)]
[12:08:58.654]            DAP_SWJ_Sequence(16, 0xE79E); //JTAG to SWD switch
[12:08:58.656]              // -> [DAP_SWJ_Sequence(16, 0x000000000000E79E)]
[12:08:58.656]            DAP_SWJ_Sequence(51, 0x0007FFFFFFFFFFFF);  //Ensure the switch is successful 
[12:08:58.658]              // -> [DAP_SWJ_Sequence(51, 0x0007FFFFFFFFFFFF)]
[12:08:58.658]            DAP_SWJ_Sequence(7,  0x00); //Give the line some time to initialize
[12:08:58.660]              // -> [DAP_SWJ_Sequence(7, 0x0000000000000000)]
[12:08:58.660]          </block>
[12:08:58.660]          // end if-block "!hasDormant"
[12:08:58.660]        </control>
[12:08:58.661]        // end if-block "isSWJ"
[12:08:58.661]      </control>
[12:08:58.661]      <control if="!isSWJ" while="" timeout="0" info="">
[12:08:58.661]        // if-block "!isSWJ"
[12:08:58.661]          // =>  FALSE
[12:08:58.661]        // skip if-block "!isSWJ"
[12:08:58.661]      </control>
[12:08:58.661]      <block atomic="false" info="">
[12:08:58.661]        ReadDP(0x0);
[12:08:58.678]  
[12:08:58.678]  !!! E310 : Debug access failed - cannot read DP register 0x00
[12:08:58.678]  
[12:08:58.679]          // -> [ReadDP(0x00000000) => 0x6BA02477]   (__dp=0x00000000)
[12:08:58.679]      </block>
[12:08:58.679]      // end if-block "protType == 2"
[12:08:58.679]    </control>
[12:08:58.679]  </sequence>
[12:08:58.679]  
[12:09:09.089]  **********  Sequence "DebugPortSetup"  (Context="Connect", Pname="", info="")
[12:09:09.090]  
[12:09:09.090]  <debugvars>
[12:09:09.090]    // Pre-defined
[12:09:09.091]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[12:09:09.091]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[12:09:09.091]    __dp=0x00000000
[12:09:09.091]    __ap=0x00000000
[12:09:09.091]    __traceout=0x00000000      (Trace Disabled)
[12:09:09.091]    __errorcontrol=0x00000000  (Skip Errors="False")
[12:09:09.091]    __FlashAddr=0x00000000
[12:09:09.092]    __FlashLen=0x00000000
[12:09:09.092]    __FlashArg=0x00000000
[12:09:09.092]    __FlashOp=0x00000000
[12:09:09.092]    __Result=0x00000000
[12:09:09.092]  </debugvars>
[12:09:09.092]  
[12:09:09.092]  <sequence name="DebugPortSetup" Pname="" disable="false" info="">
[12:09:09.092]    <block atomic="false" info="">
[12:09:09.092]      __var isSWJ      = ((__protocol & 0x00010000) != 0);
[12:09:09.092]        // -> [isSWJ <= 0x00000001]
[12:09:09.093]      __var hasDormant = __protocol & 0x00020000;
[12:09:09.093]        // -> [hasDormant <= 0x00000000]
[12:09:09.093]      __var protType   = __protocol & 0x0000FFFF;
[12:09:09.093]        // -> [protType <= 0x00000002]
[12:09:09.093]    </block>
[12:09:09.093]    <control if="protType == 1" while="" timeout="0" info="">
[12:09:09.093]      // if-block "protType == 1"
[12:09:09.093]        // =>  FALSE
[12:09:09.093]      // skip if-block "protType == 1"
[12:09:09.093]    </control>
[12:09:09.094]    <control if="protType == 2" while="" timeout="0" info="">
[12:09:09.094]      // if-block "protType == 2"
[12:09:09.094]        // =>  TRUE
[12:09:09.094]      <control if="isSWJ" while="" timeout="0" info="">
[12:09:09.094]        // if-block "isSWJ"
[12:09:09.094]          // =>  TRUE
[12:09:09.094]        <control if="hasDormant" while="" timeout="0" info="">
[12:09:09.094]          // if-block "hasDormant"
[12:09:09.094]            // =>  FALSE
[12:09:09.095]          // skip if-block "hasDormant"
[12:09:09.095]        </control>
[12:09:09.095]        <control if="!hasDormant" while="" timeout="0" info="">
[12:09:09.095]          // if-block "!hasDormant"
[12:09:09.095]            // =>  TRUE
[12:09:09.095]          <block atomic="false" info="">
[12:09:09.095]            DAP_SWJ_Sequence(51, 0x0007FFFFFFFFFFFF); //lets put the JTAG line in a reset state
[12:09:09.097]              // -> [DAP_SWJ_Sequence(51, 0x0007FFFFFFFFFFFF)]
[12:09:09.097]            DAP_SWJ_Sequence(16,0xE73C); //SWD to JTAG, this is to ensure the state machine is initialized
[12:09:09.099]              // -> [DAP_SWJ_Sequence(16, 0x000000000000E73C)]
[12:09:09.099]            DAP_SWJ_Sequence(51, 0x0007FFFFFFFFFFFF); //Put it back in reset
[12:09:09.101]              // -> [DAP_SWJ_Sequence(51, 0x0007FFFFFFFFFFFF)]
[12:09:09.101]            DAP_SWJ_Sequence(16, 0xE79E); //JTAG to SWD switch
[12:09:09.103]              // -> [DAP_SWJ_Sequence(16, 0x000000000000E79E)]
[12:09:09.103]            DAP_SWJ_Sequence(51, 0x0007FFFFFFFFFFFF);  //Ensure the switch is successful 
[12:09:09.105]              // -> [DAP_SWJ_Sequence(51, 0x0007FFFFFFFFFFFF)]
[12:09:09.105]            DAP_SWJ_Sequence(7,  0x00); //Give the line some time to initialize
[12:09:09.107]              // -> [DAP_SWJ_Sequence(7, 0x0000000000000000)]
[12:09:09.107]          </block>
[12:09:09.107]          // end if-block "!hasDormant"
[12:09:09.107]        </control>
[12:09:09.107]        // end if-block "isSWJ"
[12:09:09.107]      </control>
[12:09:09.107]      <control if="!isSWJ" while="" timeout="0" info="">
[12:09:09.107]        // if-block "!isSWJ"
[12:09:09.107]          // =>  FALSE
[12:09:09.107]        // skip if-block "!isSWJ"
[12:09:09.107]      </control>
[12:09:09.108]      <block atomic="false" info="">
[12:09:09.108]        ReadDP(0x0);
[12:09:09.122]  
[12:09:09.122]  !!! E310 : Debug access failed - cannot read DP register 0x00
[12:09:09.122]  
[12:09:09.123]          // -> [ReadDP(0x00000000) => 0x6BA02477]   (__dp=0x00000000)
[12:09:09.123]      </block>
[12:09:09.124]      // end if-block "protType == 2"
[12:09:09.124]    </control>
[12:09:09.124]  </sequence>
[12:09:09.124]  
[12:09:12.819]  **********  Sequence "DebugPortSetup"  (Context="Connect", Pname="", info="")
[12:09:12.819]  
[12:09:12.820]  <debugvars>
[12:09:12.820]    // Pre-defined
[12:09:12.820]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[12:09:12.820]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[12:09:12.821]    __dp=0x00000000
[12:09:12.821]    __ap=0x00000000
[12:09:12.821]    __traceout=0x00000000      (Trace Disabled)
[12:09:12.821]    __errorcontrol=0x00000000  (Skip Errors="False")
[12:09:12.821]    __FlashAddr=0x00000000
[12:09:12.821]    __FlashLen=0x00000000
[12:09:12.821]    __FlashArg=0x00000000
[12:09:12.821]    __FlashOp=0x00000000
[12:09:12.821]    __Result=0x00000000
[12:09:12.821]  </debugvars>
[12:09:12.822]  
[12:09:12.822]  <sequence name="DebugPortSetup" Pname="" disable="false" info="">
[12:09:12.822]    <block atomic="false" info="">
[12:09:12.822]      __var isSWJ      = ((__protocol & 0x00010000) != 0);
[12:09:12.822]        // -> [isSWJ <= 0x00000001]
[12:09:12.822]      __var hasDormant = __protocol & 0x00020000;
[12:09:12.822]        // -> [hasDormant <= 0x00000000]
[12:09:12.822]      __var protType   = __protocol & 0x0000FFFF;
[12:09:12.823]        // -> [protType <= 0x00000002]
[12:09:12.823]    </block>
[12:09:12.823]    <control if="protType == 1" while="" timeout="0" info="">
[12:09:12.823]      // if-block "protType == 1"
[12:09:12.823]        // =>  FALSE
[12:09:12.823]      // skip if-block "protType == 1"
[12:09:12.823]    </control>
[12:09:12.823]    <control if="protType == 2" while="" timeout="0" info="">
[12:09:12.823]      // if-block "protType == 2"
[12:09:12.824]        // =>  TRUE
[12:09:12.824]      <control if="isSWJ" while="" timeout="0" info="">
[12:09:12.824]        // if-block "isSWJ"
[12:09:12.824]          // =>  TRUE
[12:09:12.824]        <control if="hasDormant" while="" timeout="0" info="">
[12:09:12.824]          // if-block "hasDormant"
[12:09:12.824]            // =>  FALSE
[12:09:12.824]          // skip if-block "hasDormant"
[12:09:12.824]        </control>
[12:09:12.824]        <control if="!hasDormant" while="" timeout="0" info="">
[12:09:12.825]          // if-block "!hasDormant"
[12:09:12.825]            // =>  TRUE
[12:09:12.825]          <block atomic="false" info="">
[12:09:12.825]            DAP_SWJ_Sequence(51, 0x0007FFFFFFFFFFFF); //lets put the JTAG line in a reset state
[12:09:12.827]              // -> [DAP_SWJ_Sequence(51, 0x0007FFFFFFFFFFFF)]
[12:09:12.827]            DAP_SWJ_Sequence(16,0xE73C); //SWD to JTAG, this is to ensure the state machine is initialized
[12:09:12.829]              // -> [DAP_SWJ_Sequence(16, 0x000000000000E73C)]
[12:09:12.829]            DAP_SWJ_Sequence(51, 0x0007FFFFFFFFFFFF); //Put it back in reset
[12:09:12.831]              // -> [DAP_SWJ_Sequence(51, 0x0007FFFFFFFFFFFF)]
[12:09:12.831]            DAP_SWJ_Sequence(16, 0xE79E); //JTAG to SWD switch
[12:09:12.833]              // -> [DAP_SWJ_Sequence(16, 0x000000000000E79E)]
[12:09:12.833]            DAP_SWJ_Sequence(51, 0x0007FFFFFFFFFFFF);  //Ensure the switch is successful 
[12:09:12.835]              // -> [DAP_SWJ_Sequence(51, 0x0007FFFFFFFFFFFF)]
[12:09:12.835]            DAP_SWJ_Sequence(7,  0x00); //Give the line some time to initialize
[12:09:12.837]              // -> [DAP_SWJ_Sequence(7, 0x0000000000000000)]
[12:09:12.837]          </block>
[12:09:12.837]          // end if-block "!hasDormant"
[12:09:12.837]        </control>
[12:09:12.837]        // end if-block "isSWJ"
[12:09:12.837]      </control>
[12:09:12.837]      <control if="!isSWJ" while="" timeout="0" info="">
[12:09:12.837]        // if-block "!isSWJ"
[12:09:12.837]          // =>  FALSE
[12:09:12.837]        // skip if-block "!isSWJ"
[12:09:12.837]      </control>
[12:09:12.837]      <block atomic="false" info="">
[12:09:12.838]        ReadDP(0x0);
[12:09:12.853]  
[12:09:12.853]  !!! E310 : Debug access failed - cannot read DP register 0x00
[12:09:12.853]  
[12:09:12.854]          // -> [ReadDP(0x00000000) => 0x6BA02477]   (__dp=0x00000000)
[12:09:12.854]      </block>
[12:09:12.854]      // end if-block "protType == 2"
[12:09:12.855]    </control>
[12:09:12.855]  </sequence>
[12:09:12.855]  
[12:09:15.433]  **********  Sequence "DebugPortSetup"  (Context="Connect", Pname="", info="")
[12:09:15.433]  
[12:09:15.435]  <debugvars>
[12:09:15.435]    // Pre-defined
[12:09:15.435]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[12:09:15.435]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[12:09:15.435]    __dp=0x00000000
[12:09:15.436]    __ap=0x00000000
[12:09:15.436]    __traceout=0x00000000      (Trace Disabled)
[12:09:15.436]    __errorcontrol=0x00000000  (Skip Errors="False")
[12:09:15.436]    __FlashAddr=0x00000000
[12:09:15.436]    __FlashLen=0x00000000
[12:09:15.436]    __FlashArg=0x00000000
[12:09:15.436]    __FlashOp=0x00000000
[12:09:15.436]    __Result=0x00000000
[12:09:15.436]  </debugvars>
[12:09:15.436]  
[12:09:15.437]  <sequence name="DebugPortSetup" Pname="" disable="false" info="">
[12:09:15.437]    <block atomic="false" info="">
[12:09:15.437]      __var isSWJ      = ((__protocol & 0x00010000) != 0);
[12:09:15.437]        // -> [isSWJ <= 0x00000001]
[12:09:15.437]      __var hasDormant = __protocol & 0x00020000;
[12:09:15.437]        // -> [hasDormant <= 0x00000000]
[12:09:15.437]      __var protType   = __protocol & 0x0000FFFF;
[12:09:15.437]        // -> [protType <= 0x00000002]
[12:09:15.437]    </block>
[12:09:15.437]    <control if="protType == 1" while="" timeout="0" info="">
[12:09:15.437]      // if-block "protType == 1"
[12:09:15.438]        // =>  FALSE
[12:09:15.438]      // skip if-block "protType == 1"
[12:09:15.438]    </control>
[12:09:15.438]    <control if="protType == 2" while="" timeout="0" info="">
[12:09:15.438]      // if-block "protType == 2"
[12:09:15.438]        // =>  TRUE
[12:09:15.438]      <control if="isSWJ" while="" timeout="0" info="">
[12:09:15.438]        // if-block "isSWJ"
[12:09:15.438]          // =>  TRUE
[12:09:15.439]        <control if="hasDormant" while="" timeout="0" info="">
[12:09:15.439]          // if-block "hasDormant"
[12:09:15.439]            // =>  FALSE
[12:09:15.439]          // skip if-block "hasDormant"
[12:09:15.439]        </control>
[12:09:15.439]        <control if="!hasDormant" while="" timeout="0" info="">
[12:09:15.439]          // if-block "!hasDormant"
[12:09:15.439]            // =>  TRUE
[12:09:15.439]          <block atomic="false" info="">
[12:09:15.439]            DAP_SWJ_Sequence(51, 0x0007FFFFFFFFFFFF); //lets put the JTAG line in a reset state
[12:09:15.441]              // -> [DAP_SWJ_Sequence(51, 0x0007FFFFFFFFFFFF)]
[12:09:15.441]            DAP_SWJ_Sequence(16,0xE73C); //SWD to JTAG, this is to ensure the state machine is initialized
[12:09:15.443]              // -> [DAP_SWJ_Sequence(16, 0x000000000000E73C)]
[12:09:15.443]            DAP_SWJ_Sequence(51, 0x0007FFFFFFFFFFFF); //Put it back in reset
[12:09:15.445]              // -> [DAP_SWJ_Sequence(51, 0x0007FFFFFFFFFFFF)]
[12:09:15.445]            DAP_SWJ_Sequence(16, 0xE79E); //JTAG to SWD switch
[12:09:15.446]              // -> [DAP_SWJ_Sequence(16, 0x000000000000E79E)]
[12:09:15.447]            DAP_SWJ_Sequence(51, 0x0007FFFFFFFFFFFF);  //Ensure the switch is successful 
[12:09:15.448]              // -> [DAP_SWJ_Sequence(51, 0x0007FFFFFFFFFFFF)]
[12:09:15.449]            DAP_SWJ_Sequence(7,  0x00); //Give the line some time to initialize
[12:09:15.450]              // -> [DAP_SWJ_Sequence(7, 0x0000000000000000)]
[12:09:15.451]          </block>
[12:09:15.451]          // end if-block "!hasDormant"
[12:09:15.451]        </control>
[12:09:15.451]        // end if-block "isSWJ"
[12:09:15.451]      </control>
[12:09:15.451]      <control if="!isSWJ" while="" timeout="0" info="">
[12:09:15.451]        // if-block "!isSWJ"
[12:09:15.451]          // =>  FALSE
[12:09:15.451]        // skip if-block "!isSWJ"
[12:09:15.451]      </control>
[12:09:15.451]      <block atomic="false" info="">
[12:09:15.451]        ReadDP(0x0);
[12:09:15.465]  
[12:09:15.465]  !!! E310 : Debug access failed - cannot read DP register 0x00
[12:09:15.465]  
[12:09:15.465]          // -> [ReadDP(0x00000000) => 0x6BA02477]   (__dp=0x00000000)
[12:09:15.466]      </block>
[12:09:15.466]      // end if-block "protType == 2"
[12:09:15.466]    </control>
[12:09:15.466]  </sequence>
[12:09:15.466]  
[12:09:22.400]  **********  Sequence "DebugPortSetup"  (Context="Connect", Pname="", info="")
[12:09:22.400]  
[12:09:22.400]  <debugvars>
[12:09:22.401]    // Pre-defined
[12:09:22.401]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[12:09:22.401]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[12:09:22.401]    __dp=0x00000000
[12:09:22.401]    __ap=0x00000000
[12:09:22.401]    __traceout=0x00000000      (Trace Disabled)
[12:09:22.401]    __errorcontrol=0x00000000  (Skip Errors="False")
[12:09:22.401]    __FlashAddr=0x00000000
[12:09:22.401]    __FlashLen=0x00000000
[12:09:22.401]    __FlashArg=0x00000000
[12:09:22.401]    __FlashOp=0x00000000
[12:09:22.401]    __Result=0x00000000
[12:09:22.401]  </debugvars>
[12:09:22.401]  
[12:09:22.401]  <sequence name="DebugPortSetup" Pname="" disable="false" info="">
[12:09:22.401]    <block atomic="false" info="">
[12:09:22.402]      __var isSWJ      = ((__protocol & 0x00010000) != 0);
[12:09:22.402]        // -> [isSWJ <= 0x00000001]
[12:09:22.402]      __var hasDormant = __protocol & 0x00020000;
[12:09:22.402]        // -> [hasDormant <= 0x00000000]
[12:09:22.402]      __var protType   = __protocol & 0x0000FFFF;
[12:09:22.402]        // -> [protType <= 0x00000002]
[12:09:22.402]    </block>
[12:09:22.402]    <control if="protType == 1" while="" timeout="0" info="">
[12:09:22.402]      // if-block "protType == 1"
[12:09:22.402]        // =>  FALSE
[12:09:22.402]      // skip if-block "protType == 1"
[12:09:22.402]    </control>
[12:09:22.402]    <control if="protType == 2" while="" timeout="0" info="">
[12:09:22.402]      // if-block "protType == 2"
[12:09:22.402]        // =>  TRUE
[12:09:22.402]      <control if="isSWJ" while="" timeout="0" info="">
[12:09:22.402]        // if-block "isSWJ"
[12:09:22.403]          // =>  TRUE
[12:09:22.403]        <control if="hasDormant" while="" timeout="0" info="">
[12:09:22.403]          // if-block "hasDormant"
[12:09:22.403]            // =>  FALSE
[12:09:22.403]          // skip if-block "hasDormant"
[12:09:22.403]        </control>
[12:09:22.403]        <control if="!hasDormant" while="" timeout="0" info="">
[12:09:22.403]          // if-block "!hasDormant"
[12:09:22.403]            // =>  TRUE
[12:09:22.403]          <block atomic="false" info="">
[12:09:22.403]            DAP_SWJ_Sequence(51, 0x0007FFFFFFFFFFFF); //lets put the JTAG line in a reset state
[12:09:22.405]              // -> [DAP_SWJ_Sequence(51, 0x0007FFFFFFFFFFFF)]
[12:09:22.405]            DAP_SWJ_Sequence(16,0xE73C); //SWD to JTAG, this is to ensure the state machine is initialized
[12:09:22.408]              // -> [DAP_SWJ_Sequence(16, 0x000000000000E73C)]
[12:09:22.408]            DAP_SWJ_Sequence(51, 0x0007FFFFFFFFFFFF); //Put it back in reset
[12:09:22.410]              // -> [DAP_SWJ_Sequence(51, 0x0007FFFFFFFFFFFF)]
[12:09:22.410]            DAP_SWJ_Sequence(16, 0xE79E); //JTAG to SWD switch
[12:09:22.412]              // -> [DAP_SWJ_Sequence(16, 0x000000000000E79E)]
[12:09:22.412]            DAP_SWJ_Sequence(51, 0x0007FFFFFFFFFFFF);  //Ensure the switch is successful 
[12:09:22.414]              // -> [DAP_SWJ_Sequence(51, 0x0007FFFFFFFFFFFF)]
[12:09:22.414]            DAP_SWJ_Sequence(7,  0x00); //Give the line some time to initialize
[12:09:22.416]              // -> [DAP_SWJ_Sequence(7, 0x0000000000000000)]
[12:09:22.416]          </block>
[12:09:22.416]          // end if-block "!hasDormant"
[12:09:22.416]        </control>
[12:09:22.416]        // end if-block "isSWJ"
[12:09:22.416]      </control>
[12:09:22.417]      <control if="!isSWJ" while="" timeout="0" info="">
[12:09:22.417]        // if-block "!isSWJ"
[12:09:22.417]          // =>  FALSE
[12:09:22.417]        // skip if-block "!isSWJ"
[12:09:22.417]      </control>
[12:09:22.417]      <block atomic="false" info="">
[12:09:22.417]        ReadDP(0x0);
[12:09:22.430]  
[12:09:22.430]  !!! E310 : Debug access failed - cannot read DP register 0x00
[12:09:22.430]  
[12:09:22.431]          // -> [ReadDP(0x00000000) => 0x6BA02477]   (__dp=0x00000000)
[12:09:22.431]      </block>
[12:09:22.431]      // end if-block "protType == 2"
[12:09:22.431]    </control>
[12:09:22.431]  </sequence>
[12:09:22.432]  
[12:09:25.843]  **********  Sequence "DebugPortSetup"  (Context="Connect", Pname="", info="")
[12:09:25.843]  
[12:09:25.844]  <debugvars>
[12:09:25.844]    // Pre-defined
[12:09:25.844]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[12:09:25.844]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[12:09:25.844]    __dp=0x00000000
[12:09:25.844]    __ap=0x00000000
[12:09:25.844]    __traceout=0x00000000      (Trace Disabled)
[12:09:25.844]    __errorcontrol=0x00000000  (Skip Errors="False")
[12:09:25.844]    __FlashAddr=0x00000000
[12:09:25.845]    __FlashLen=0x00000000
[12:09:25.845]    __FlashArg=0x00000000
[12:09:25.845]    __FlashOp=0x00000000
[12:09:25.845]    __Result=0x00000000
[12:09:25.845]  </debugvars>
[12:09:25.845]  
[12:09:25.845]  <sequence name="DebugPortSetup" Pname="" disable="false" info="">
[12:09:25.845]    <block atomic="false" info="">
[12:09:25.845]      __var isSWJ      = ((__protocol & 0x00010000) != 0);
[12:09:25.846]        // -> [isSWJ <= 0x00000001]
[12:09:25.846]      __var hasDormant = __protocol & 0x00020000;
[12:09:25.846]        // -> [hasDormant <= 0x00000000]
[12:09:25.846]      __var protType   = __protocol & 0x0000FFFF;
[12:09:25.846]        // -> [protType <= 0x00000002]
[12:09:25.846]    </block>
[12:09:25.846]    <control if="protType == 1" while="" timeout="0" info="">
[12:09:25.846]      // if-block "protType == 1"
[12:09:25.846]        // =>  FALSE
[12:09:25.846]      // skip if-block "protType == 1"
[12:09:25.847]    </control>
[12:09:25.847]    <control if="protType == 2" while="" timeout="0" info="">
[12:09:25.847]      // if-block "protType == 2"
[12:09:25.847]        // =>  TRUE
[12:09:25.847]      <control if="isSWJ" while="" timeout="0" info="">
[12:09:25.847]        // if-block "isSWJ"
[12:09:25.847]          // =>  TRUE
[12:09:25.847]        <control if="hasDormant" while="" timeout="0" info="">
[12:09:25.847]          // if-block "hasDormant"
[12:09:25.847]            // =>  FALSE
[12:09:25.847]          // skip if-block "hasDormant"
[12:09:25.847]        </control>
[12:09:25.848]        <control if="!hasDormant" while="" timeout="0" info="">
[12:09:25.848]          // if-block "!hasDormant"
[12:09:25.848]            // =>  TRUE
[12:09:25.848]          <block atomic="false" info="">
[12:09:25.848]            DAP_SWJ_Sequence(51, 0x0007FFFFFFFFFFFF); //lets put the JTAG line in a reset state
[12:09:25.850]              // -> [DAP_SWJ_Sequence(51, 0x0007FFFFFFFFFFFF)]
[12:09:25.850]            DAP_SWJ_Sequence(16,0xE73C); //SWD to JTAG, this is to ensure the state machine is initialized
[12:09:25.853]              // -> [DAP_SWJ_Sequence(16, 0x000000000000E73C)]
[12:09:25.853]            DAP_SWJ_Sequence(51, 0x0007FFFFFFFFFFFF); //Put it back in reset
[12:09:25.855]              // -> [DAP_SWJ_Sequence(51, 0x0007FFFFFFFFFFFF)]
[12:09:25.855]            DAP_SWJ_Sequence(16, 0xE79E); //JTAG to SWD switch
[12:09:25.857]              // -> [DAP_SWJ_Sequence(16, 0x000000000000E79E)]
[12:09:25.857]            DAP_SWJ_Sequence(51, 0x0007FFFFFFFFFFFF);  //Ensure the switch is successful 
[12:09:25.859]              // -> [DAP_SWJ_Sequence(51, 0x0007FFFFFFFFFFFF)]
[12:09:25.859]            DAP_SWJ_Sequence(7,  0x00); //Give the line some time to initialize
[12:09:25.861]              // -> [DAP_SWJ_Sequence(7, 0x0000000000000000)]
[12:09:25.861]          </block>
[12:09:25.861]          // end if-block "!hasDormant"
[12:09:25.861]        </control>
[12:09:25.861]        // end if-block "isSWJ"
[12:09:25.861]      </control>
[12:09:25.861]      <control if="!isSWJ" while="" timeout="0" info="">
[12:09:25.861]        // if-block "!isSWJ"
[12:09:25.861]          // =>  FALSE
[12:09:25.861]        // skip if-block "!isSWJ"
[12:09:25.861]      </control>
[12:09:25.861]      <block atomic="false" info="">
[12:09:25.861]        ReadDP(0x0);
[12:09:25.874]  
[12:09:25.874]  !!! E310 : Debug access failed - cannot read DP register 0x00
[12:09:25.874]  
[12:09:25.876]          // -> [ReadDP(0x00000000) => 0x6BA02477]   (__dp=0x00000000)
[12:09:25.876]      </block>
[12:09:25.876]      // end if-block "protType == 2"
[12:09:25.876]    </control>
[12:09:25.876]  </sequence>
[12:09:25.876]  
[12:09:28.145]  **********  Sequence "DebugPortSetup"  (Context="Connect", Pname="", info="")
[12:09:28.145]  
[12:09:28.146]  <debugvars>
[12:09:28.146]    // Pre-defined
[12:09:28.146]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[12:09:28.146]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[12:09:28.146]    __dp=0x00000000
[12:09:28.147]    __ap=0x00000000
[12:09:28.147]    __traceout=0x00000000      (Trace Disabled)
[12:09:28.147]    __errorcontrol=0x00000000  (Skip Errors="False")
[12:09:28.147]    __FlashAddr=0x00000000
[12:09:28.147]    __FlashLen=0x00000000
[12:09:28.147]    __FlashArg=0x00000000
[12:09:28.147]    __FlashOp=0x00000000
[12:09:28.147]    __Result=0x00000000
[12:09:28.147]  </debugvars>
[12:09:28.147]  
[12:09:28.148]  <sequence name="DebugPortSetup" Pname="" disable="false" info="">
[12:09:28.148]    <block atomic="false" info="">
[12:09:28.148]      __var isSWJ      = ((__protocol & 0x00010000) != 0);
[12:09:28.148]        // -> [isSWJ <= 0x00000001]
[12:09:28.148]      __var hasDormant = __protocol & 0x00020000;
[12:09:28.148]        // -> [hasDormant <= 0x00000000]
[12:09:28.148]      __var protType   = __protocol & 0x0000FFFF;
[12:09:28.148]        // -> [protType <= 0x00000002]
[12:09:28.148]    </block>
[12:09:28.148]    <control if="protType == 1" while="" timeout="0" info="">
[12:09:28.148]      // if-block "protType == 1"
[12:09:28.149]        // =>  FALSE
[12:09:28.149]      // skip if-block "protType == 1"
[12:09:28.149]    </control>
[12:09:28.149]    <control if="protType == 2" while="" timeout="0" info="">
[12:09:28.149]      // if-block "protType == 2"
[12:09:28.149]        // =>  TRUE
[12:09:28.149]      <control if="isSWJ" while="" timeout="0" info="">
[12:09:28.149]        // if-block "isSWJ"
[12:09:28.149]          // =>  TRUE
[12:09:28.149]        <control if="hasDormant" while="" timeout="0" info="">
[12:09:28.149]          // if-block "hasDormant"
[12:09:28.149]            // =>  FALSE
[12:09:28.150]          // skip if-block "hasDormant"
[12:09:28.150]        </control>
[12:09:28.150]        <control if="!hasDormant" while="" timeout="0" info="">
[12:09:28.150]          // if-block "!hasDormant"
[12:09:28.150]            // =>  TRUE
[12:09:28.150]          <block atomic="false" info="">
[12:09:28.150]            DAP_SWJ_Sequence(51, 0x0007FFFFFFFFFFFF); //lets put the JTAG line in a reset state
[12:09:28.152]              // -> [DAP_SWJ_Sequence(51, 0x0007FFFFFFFFFFFF)]
[12:09:28.152]            DAP_SWJ_Sequence(16,0xE73C); //SWD to JTAG, this is to ensure the state machine is initialized
[12:09:28.154]              // -> [DAP_SWJ_Sequence(16, 0x000000000000E73C)]
[12:09:28.154]            DAP_SWJ_Sequence(51, 0x0007FFFFFFFFFFFF); //Put it back in reset
[12:09:28.156]              // -> [DAP_SWJ_Sequence(51, 0x0007FFFFFFFFFFFF)]
[12:09:28.156]            DAP_SWJ_Sequence(16, 0xE79E); //JTAG to SWD switch
[12:09:28.158]              // -> [DAP_SWJ_Sequence(16, 0x000000000000E79E)]
[12:09:28.158]            DAP_SWJ_Sequence(51, 0x0007FFFFFFFFFFFF);  //Ensure the switch is successful 
[12:09:28.160]              // -> [DAP_SWJ_Sequence(51, 0x0007FFFFFFFFFFFF)]
[12:09:28.160]            DAP_SWJ_Sequence(7,  0x00); //Give the line some time to initialize
[12:09:28.162]              // -> [DAP_SWJ_Sequence(7, 0x0000000000000000)]
[12:09:28.162]          </block>
[12:09:28.162]          // end if-block "!hasDormant"
[12:09:28.162]        </control>
[12:09:28.162]        // end if-block "isSWJ"
[12:09:28.162]      </control>
[12:09:28.163]      <control if="!isSWJ" while="" timeout="0" info="">
[12:09:28.163]        // if-block "!isSWJ"
[12:09:28.163]          // =>  FALSE
[12:09:28.163]        // skip if-block "!isSWJ"
[12:09:28.163]      </control>
[12:09:28.163]      <block atomic="false" info="">
[12:09:28.163]        ReadDP(0x0);
[12:09:28.176]  
[12:09:28.176]  !!! E310 : Debug access failed - cannot read DP register 0x00
[12:09:28.176]  
[12:09:28.178]          // -> [ReadDP(0x00000000) => 0x6BA02477]   (__dp=0x00000000)
[12:09:28.178]      </block>
[12:09:28.178]      // end if-block "protType == 2"
[12:09:28.178]    </control>
[12:09:28.178]  </sequence>
[12:09:28.178]  
