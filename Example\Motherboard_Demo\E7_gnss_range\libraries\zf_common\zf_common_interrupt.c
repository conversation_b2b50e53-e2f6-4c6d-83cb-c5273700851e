/*********************************************************************************************************************
* MSPM0G3507 Opensource Library 即（MSPM0G3507 开源库）是一个基于官方 SDK 接口的第三方开源库
* Copyright (c) 2022 SEEKFREE 逐飞科技
* 
* 本文件是 MSPM0G3507 开源库的一部分
* 
* MSPM0G3507 开源库 是免费软件
* 您可以根据自由软件基金会发布的 GPL（GNU General Public License，即 GNU通用公共许可证）的条款
* 即 GPL 的第3版（即 GPL3.0）或（您选择的）任何后来的版本，重新发布和/或修改它
* 
* 本开源库的发布是希望它能发挥作用，但并未对其作任何的保证
* 甚至没有隐含的适销性或适合特定用途的保证
* 更多细节请参见 GPL
* 
* 您应该在收到本开源库的同时收到一份 GPL 的副本
* 如果没有，请参阅<https://www.gnu.org/licenses/>
* 
* 额外注明：
* 本开源库使用 GPL3.0 开源许可证协议 以上许可申明为译文版本
* 许可申明英文版在 libraries/doc 文件夹下的 GPL3_permission_statement.txt 文件中
* 许可证副本在 libraries 文件夹下 即该文件夹下的 LICENSE 文件
* 欢迎各位使用并传播本程序 但修改内容时必须保留逐飞科技的版权声明（即本声明）
* 
* 文件名称          zf_common_interrupt
* 公司名称          成都逐飞科技有限公司
* 版本信息          查看 libraries/doc 文件夹内 version 文件 版本说明
* 开发环境          MDK 5.37
* 适用平台          MSPM0G3507
* 店铺链接          https://seekfree.taobao.com/
* 
* 修改记录
* 日期              作者                备注
* 2025-06-1        SeekFree            first version
********************************************************************************************************************/

#include "zf_common_interrupt.h"

static uint32 interrupt_nest_count = 0;

//-------------------------------------------------------------------------------------------------------------------
// 函数简介     全局中断使能
// 参数说明     primask         当前屏蔽嵌套层数
// 返回参数     void
// 使用示例     interrupt_global_enable(primask);
// 备注信息     
//-------------------------------------------------------------------------------------------------------------------
void interrupt_global_enable (uint32 primask)
{
    if(primask)
    {
        interrupt_nest_count --;
    }
    if(!interrupt_nest_count)
    {
        __ASM volatile("cpsie i");
    }
}

//-------------------------------------------------------------------------------------------------------------------
// 函数简介     全局中断屏蔽
// 参数说明     void
// 返回参数     uint32          当前屏蔽嵌套层数
// 使用示例     primask = interrupt_global_disable();
// 备注信息     
//-------------------------------------------------------------------------------------------------------------------
uint32 interrupt_global_disable (void)
{
    if(!interrupt_nest_count)
    {
        __ASM volatile("cpsid i");
    }
    interrupt_nest_count ++;
    return interrupt_nest_count;
}

//-------------------------------------------------------------------------------------------------------------------
// 函数简介     指定中断使能
// 参数说明     irqn            指定中断号 可查看 isr.c 对应中断服务函数的标注
// 返回参数     void
// 使用示例     interrupt_enable(UART1_IRQn);
// 备注信息     
//-------------------------------------------------------------------------------------------------------------------
void interrupt_enable (IRQn_Type irqn)
{
    NVIC_EnableIRQ(irqn);
}

//-------------------------------------------------------------------------------------------------------------------
// 函数简介     指定中断屏蔽
// 参数说明     irqn            指定中断号 可查看 isr.c 对应中断服务函数的标注
// 返回参数     void
// 使用示例     interrupt_disable(UART1_IRQn);
// 备注信息     
//-------------------------------------------------------------------------------------------------------------------
void interrupt_disable (IRQn_Type irqn)
{
    NVIC_DisableIRQ(irqn);
}

//-------------------------------------------------------------------------------------------------------------------
// 函数简介     指定中断设置优先级
// 参数说明     irqn            指定中断号 可查看 isr.c 对应中断服务函数的标注
// 参数说明     priority        中断优先级 0-7 越低越高
// 返回参数     void
// 使用示例     interrupt_set_priority(UART1_IRQn, 0);
// 备注信息     
//-------------------------------------------------------------------------------------------------------------------
void interrupt_set_priority (IRQn_Type irqn, uint8 priority)
{
    NVIC_SetPriority(irqn, priority);
}

//-------------------------------------------------------------------------------------------------------------------
// 函数简介     中断组初始化
// 参数说明     void
// 返回参数     void
// 使用示例     interrupt_init();
// 备注信息     会在  clock_init 内部调用
//-------------------------------------------------------------------------------------------------------------------
void interrupt_init (void)
{
    NVIC_SetPriorityGrouping(4);
}
