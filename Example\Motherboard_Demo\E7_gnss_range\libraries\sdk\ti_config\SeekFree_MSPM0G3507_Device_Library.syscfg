/**
 * These arguments were used when this file was generated. They will be automatically applied on subsequent loads
 * via the GUI or CLI. Run CLI with '--help' for additional information on how to override these arguments.
 * @cliArgs --device "MSPM0G350X" --part "Default" --package "LQFP-64(PM)" --product "mspm0_sdk@**********"
 * @v2CliArgs --device "MSPM0G3507" --package "LQFP-64(PM)" --product "mspm0_sdk@**********"
 * @versions {"tool":"1.23.1+4034"}
 */

/**
 * Import the modules used in this configuration.
 */
const GPIO    = scripting.addModule("/ti/driverlib/GPIO", {}, false);
const GPIO1   = GPIO.addInstance();
const GPIO2   = GPIO.addInstance();
const SYSCTL  = scripting.addModule("/ti/driverlib/SYSCTL");
const SYSTICK = scripting.addModule("/ti/driverlib/SYSTICK");
const VREF    = scripting.addModule("/ti/driverlib/VREF");

/**
 * Write custom configuration values to the imported modules.
 */
const divider2       = system.clockTree["HFCLK4MFPCLKDIV"];
divider2.divideValue = 10;

const divider9       = system.clockTree["UDIV"];
divider9.divideValue = 2;

const gate7  = system.clockTree["MFCLKGATE"];
gate7.enable = true;

const gate8  = system.clockTree["MFPCLKGATE"];
gate8.enable = true;

const multiplier2         = system.clockTree["PLL_QDIV"];
multiplier2.multiplyValue = 4;

const mux4       = system.clockTree["EXHFMUX"];
mux4.inputSelect = "EXHFMUX_XTAL";

const mux5       = system.clockTree["EXLFMUX"];
mux5.inputSelect = "EXLFMUX_XTAL";

const mux8       = system.clockTree["HSCLKMUX"];
mux8.inputSelect = "HSCLKMUX_SYSPLL0";

const mux9       = system.clockTree["LFXTMUX"];
mux9.inputSelect = "LFXTMUX_TRUE";

const mux12       = system.clockTree["SYSPLLMUX"];
mux12.inputSelect = "zSYSPLLMUX_HFCLK";

const pinFunction4     = system.clockTree["HFXT"];
pinFunction4.inputFreq = 40;
pinFunction4.enable    = true;

const pinFunction6                        = system.clockTree["LFXT"];
pinFunction6.inputFreq                    = 32.768;
pinFunction6.enable                       = true;
pinFunction6.peripheral.$assign           = "SYSCTL";
pinFunction6.peripheral.lfxInPin.$assign  = "PA3";
pinFunction6.peripheral.lfxOutPin.$assign = "PA4";

GPIO1.port                               = "PORTA";
GPIO1.$name                              = "LED_A14";
GPIO1.associatedPins[0].internalResistor = "PULL_DOWN";
GPIO1.associatedPins[0].$name            = "PIN_14";
GPIO1.associatedPins[0].assignedPin      = "14";
GPIO1.associatedPins[0].pin.$assign      = "PA14";

const Board = scripting.addModule("/ti/driverlib/Board", {}, false);

GPIO2.port                               = "PORTA";
GPIO2.$name                              = "KEY_BSL";
GPIO2.associatedPins[0].$name            = "PIN_18";
GPIO2.associatedPins[0].direction        = "INPUT";
GPIO2.associatedPins[0].assignedPin      = "18";
GPIO2.associatedPins[0].internalResistor = "PULL_DOWN";
GPIO2.associatedPins[0].interruptEn      = true;
GPIO2.associatedPins[0].polarity         = "RISE";
GPIO2.associatedPins[0].pin.$assign      = "PA18";

SYSCTL.forceDefaultClkConfig = true;
SYSCTL.clockTreeEn           = true;
scripting.suppress("For best practices when the CPUCLK is running at 32MHz and above, clear the flash status bit using DL_FlashCTL_executeClearStatus\\(\\) before executing any flash operation\\. Otherwise there may be false positives\\.", SYSCTL);

SYSTICK.periodEnable      = true;
SYSTICK.interruptPriority = "0";
SYSTICK.systickEnable     = true;
SYSTICK.period            = 80;

VREF.basicVrefPins                 = "VREF+-";
VREF.basicIntVolt                  = "DL_VREF_BUFCONFIG_OUTPUT_2_5V";
VREF.basicMode                     = "DL_VREF_ENABLE_DISABLE";
VREF.basicExtVolt                  = 3.3;
VREF.vrefPosPinConfig.$name        = "ti_driverlib_gpio_GPIOPinGeneric29";
VREF.peripheral.$assign            = "VREF";
VREF.peripheral.vrefPosPin.$assign = "PA23";
VREF.peripheral.vrefNegPin.$assign = "PA21";
VREF.vrefNegPinConfig.$name        = "ti_driverlib_gpio_GPIOPinGeneric30";

/**
 * Pinmux solution for unlocked pins/peripherals. This ensures that minor changes to the automatic solver in a future
 * version of the tool will not impact the pinmux you originally saw.  These lines can be completely deleted in order to
 * re-solve from scratch.
 */
pinFunction4.peripheral.$suggestSolution           = "SYSCTL";
pinFunction4.peripheral.hfxInPin.$suggestSolution  = "PA5";
pinFunction4.peripheral.hfxOutPin.$suggestSolution = "PA6";
Board.peripheral.$suggestSolution                  = "DEBUGSS";
Board.peripheral.swclkPin.$suggestSolution         = "PA20";
Board.peripheral.swdioPin.$suggestSolution         = "PA19";
