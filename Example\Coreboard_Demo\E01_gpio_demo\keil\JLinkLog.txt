T8CE4 000:004.903   SEGGER J-Link V8.56 Log File
T8CE4 000:005.032   DLL Compiled: Jul 30 2025 11:58:21
T8CE4 000:005.038   Logging started @ 2025-08-01 04:05
T8CE4 000:005.044   Process: C:\keil5\UV4\UV4.exe
T8CE4 000:005.058 - 5.051ms 
T8CE4 000:005.070 JLINK_SetWarnOutHandler(...)
T8CE4 000:005.075 - 0.007ms 
T8CE4 000:005.084 JLINK_OpenEx(...)
T8CE4 000:008.001   Firmware: J-<PERSON> OB-STM32F072-128KB-CortexM compiled Oct 30 2023 12:11:14
T8CE4 000:009.038   Firmware: J-Link OB-STM32F072-128KB-CortexM compiled Oct 30 2023 12:11:14
T8CE4 000:009.180   Decompressing FW timestamp took 108 us
T8CE4 000:024.490   Hardware: V1.00
T8CE4 000:024.513   S/N: 4294967295
T8CE4 000:024.521   OEM: SEGGER
T8CE4 000:024.529   Feature(s): GDB, FlashDL, FlashBP, JFlash
T8CE4 000:025.131   Bootloader: (FW returned invalid version)
T8CE4 000:025.601   USB speed mode: Full speed (12 MBit/s)
T8CE4 000:025.940   TELNET listener socket opened on port 19021
T8CE4 000:026.078   WEBSRV WEBSRV_Init(): Starting webserver thread(s)
T8CE4 000:026.218   WEBSRV Webserver running on local port 19080
T8CE4 000:026.373   Looking for J-Link GUI Server exe at: C:\keil5\ARM\Segger\JLinkGUIServer.exe
T8CE4 000:026.463   Looking for J-Link GUI Server exe at: C:\Program Files\SEGGER\JLink_V856\JLinkGUIServer.exe
T8CE4 000:026.492   Forking J-Link GUI Server: C:\Program Files\SEGGER\JLink_V856\JLinkGUIServer.exe
T8CE4 000:199.557   J-Link GUI Server info: "J-Link GUI server V8.56 "
T8CE4 000:202.929 - 197.839ms returns "O.K."
T8CE4 000:202.959 JLINK_GetEmuCaps()
T8CE4 000:202.967 - 0.006ms returns 0xB8EA5A33
T8CE4 000:202.976 JLINK_TIF_GetAvailable(...)
T8CE4 000:203.197 - 0.221ms 
T8CE4 000:203.237 JLINK_SetErrorOutHandler(...)
T8CE4 000:203.245 - 0.007ms 
T8CE4 000:203.286 JLINK_ExecCommand("ProjectFile = "C:\Users\<USER>\Desktop\MSPM0G3507_Library-master\Example\Coreboard_Demo\E01_gpio_demo\keil\JLinkSettings.ini"", ...). 
T8CE4 000:214.655   Ref file found at: C:\keil5\ARM\Segger\JLinkDevices.ref
T8CE4 000:214.732   REF file references invalid XML file: C:\Program Files\SEGGER\JLink_V856\JLinkDevices.xml
T8CE4 000:215.687 - 12.404ms returns 0x00
T8CE4 000:217.334 JLINK_ExecCommand("Device = MSPM0G3507", ...). 
T8CE4 000:219.920   Device "MSPM0G3507" selected.
T8CE4 000:220.352 - 2.996ms returns 0x00
T8CE4 000:220.366 JLINK_ExecCommand("DisableConnectionTimeout", ...). 
T8CE4 000:220.381   ERROR: Unknown command
T8CE4 000:220.390 - 0.016ms returns 0x01
T8CE4 000:220.400 JLINK_GetHardwareVersion()
T8CE4 000:220.406 - 0.006ms returns 10000
T8CE4 000:220.412 JLINK_GetDLLVersion()
T8CE4 000:220.418 - 0.006ms returns 85600
T8CE4 000:220.425 JLINK_GetOEMString(...)
T8CE4 000:220.432 JLINK_GetFirmwareString(...)
T8CE4 000:220.437 - 0.005ms 
T8CE4 000:223.934 JLINK_GetDLLVersion()
T8CE4 000:223.951 - 0.016ms returns 85600
T8CE4 000:223.958 JLINK_GetCompileDateTime()
T8CE4 000:223.966 - 0.007ms 
T8CE4 000:228.799 JLINK_GetFirmwareString(...)
T8CE4 000:228.823 - 0.023ms 
T8CE4 000:229.988 JLINK_GetHardwareVersion()
T8CE4 000:230.009 - 0.020ms returns 10000
T8CE4 000:231.031 JLINK_GetSN()
T8CE4 000:231.047 - 0.016ms returns -1
T8CE4 000:231.053 JLINK_GetOEMString(...)
T8CE4 000:233.913 JLINK_TIF_Select(JLINKARM_TIF_SWD)
T8CE4 000:234.643 - 0.734ms returns 0x00
T8CE4 000:234.667 JLINK_HasError()
T8CE4 000:234.684 JLINK_SetSpeed(5000)
T8CE4 000:234.757 - 0.073ms 
T8CE4 000:234.772 JLINK_GetId()
T8CE4 000:236.354   InitTarget() start
T8CE4 000:236.383    J-Link Script File: Executing InitTarget()
T8CE4 000:240.163   DAP initialized successfully.
T8CE4 000:241.983   Setting up LPM debug bits
T8CE4 000:244.006   InitTarget() end - Took 6.59ms
T8CE4 000:245.966   Found SW-DP with ID 0x6BA02477
T8CE4 000:250.646   DPIDR: 0x6BA02477
T8CE4 000:252.081   CoreSight SoC-400 or earlier
T8CE4 000:253.155   Scanning AP map to find all available APs
T8CE4 000:260.361   AP[5]: Stopped AP scan as end of AP map has been reached
T8CE4 000:261.701   AP[0]: AHB-AP (IDR: 0x84770001, ADDR: 0x00000000)
T8CE4 000:262.852   AP[1]: MEM-AP (IDR: 0x002E0001, ADDR: 0x01000000)
T8CE4 000:264.185   AP[2]: JTAG-AP (IDR: 0x002E0000, ADDR: 0x02000000)
T8CE4 000:265.221   AP[3]: MEM-AP (IDR: 0x002E0003, ADDR: 0x03000000)
T8CE4 000:266.523   AP[4]: MEM-AP (IDR: 0x002E0002, ADDR: 0x04000000)
T8CE4 000:267.660   Iterating through AP map to find AHB-AP to use
T8CE4 000:270.439   AP[0]: Core found
T8CE4 000:273.066   AP[0]: AHB-AP ROM base: 0xF0000000
T8CE4 000:274.756   CPUID register: 0x410CC601. Implementer code: 0x41 (ARM)
T8CE4 000:275.999   Found Cortex-M0 r0p1, Little endian.
T8CE4 000:276.388   -- Max. mem block: 0x000019B8
T8CE4 000:277.993   Cortex-M: The connected J-Link (S/N 4294967295) uses an old firmware module: V1 (current is 3)
T8CE4 000:278.030   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T8CE4 000:278.577   CPU_WriteMem(4 bytes @ 0xE000EDF0)
T8CE4 000:279.167   CPU_ReadMem(4 bytes @ 0x********)
T8CE4 000:280.931   FPUnit: 4 code (BP) slots and 0 literal slots
T8CE4 000:280.951   CPU_ReadMem(4 bytes @ 0xE000EDFC)
T8CE4 000:281.362   CPU_WriteMem(4 bytes @ 0xE000EDFC)
T8CE4 000:281.842   CPU_ReadMem(4 bytes @ 0x********)
T8CE4 000:282.255   CPU_WriteMem(4 bytes @ 0x********)
T8CE4 000:284.149   CoreSight components:
T8CE4 000:285.446   ROMTbl[0] @ F0000000
T8CE4 000:285.471   CPU_ReadMem(64 bytes @ 0xF0000000)
T8CE4 000:286.506   CPU_ReadMem(32 bytes @ 0xE00FFFE0)
T8CE4 000:291.270   [0][0]: E00FF000 CID B105100D PID 000BB4C0 ROM Table
T8CE4 000:292.493   ROMTbl[1] @ E00FF000
T8CE4 000:292.523   CPU_ReadMem(64 bytes @ 0xE00FF000)
T8CE4 000:293.529   CPU_ReadMem(32 bytes @ 0xE000EFE0)
T8CE4 000:295.541   [1][0]: E000E000 CID B105E00D PID 000BB008 SCS
T8CE4 000:295.564   CPU_ReadMem(32 bytes @ 0xE0001FE0)
T8CE4 000:297.713   [1][1]: ******** CID B105E00D PID 000BB00A DWT
T8CE4 000:297.734   CPU_ReadMem(32 bytes @ 0xE0002FE0)
T8CE4 000:299.746   [1][2]: ******** CID B105E00D PID 000BB00B FPB
T8CE4 000:299.768   CPU_ReadMem(32 bytes @ 0x40402FE0)
T8CE4 000:301.502   [0][2]: 40402000 CID B105900D PID 001BB932 MTB-M0+
T8CE4 000:301.822 - 67.049ms returns 0x6BA02477
T8CE4 000:301.863 JLINK_GetDLLVersion()
T8CE4 000:301.870 - 0.006ms returns 85600
T8CE4 000:301.877 JLINK_CORE_GetFound()
T8CE4 000:301.883 - 0.006ms returns 0x60000FF
T8CE4 000:301.890 JLINK_GetDebugInfo(0x100 = JLINKARM_ROM_TABLE_ADDR_INDEX)
T8CE4 000:301.898   Value=0xF0000000
T8CE4 000:301.906 - 0.016ms returns 0
T8CE4 000:303.026 JLINK_GetDebugInfo(0x100 = JLINKARM_ROM_TABLE_ADDR_INDEX)
T8CE4 000:303.045   Value=0xF0000000
T8CE4 000:303.054 - 0.028ms returns 0
T8CE4 000:303.061 JLINK_GetDebugInfo(0x101 = JLINKARM_DEBUG_INFO_ETM_ADDR_INDEX)
T8CE4 000:303.066   Value=0x00000000
T8CE4 000:303.074 - 0.013ms returns 0
T8CE4 000:303.081 JLINK_ReadMemEx(0xE0041FF0, 0x10 Bytes, Flags = 0x02000004)
T8CE4 000:303.115   CPU_ReadMem(16 bytes @ 0xE0041FF0)
T8CE4 000:303.765   Data:  00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
T8CE4 000:303.857 - 0.775ms returns 16 (0x10)
T8CE4 000:303.902 JLINK_GetDebugInfo(0x102 = JLINKARM_DEBUG_INFO_MTB_ADDR_INDEX)
T8CE4 000:303.936   Value=0x40402000
T8CE4 000:303.954 - 0.054ms returns 0
T8CE4 000:303.960 JLINK_CORE_GetFound()
T8CE4 000:303.966 - 0.006ms returns 0x60000FF
T8CE4 000:303.972 JLINK_GetDebugInfo(0x103 = JLINKARM_DEBUG_INFO_TPIU_ADDR_INDEX)
T8CE4 000:303.978   Value=0x00000000
T8CE4 000:303.986 - 0.013ms returns 0
T8CE4 000:303.992 JLINK_GetDebugInfo(0x104 = JLINKARM_DEBUG_INFO_ITM_ADDR_INDEX)
T8CE4 000:303.997   Value=0xE0000000
T8CE4 000:304.005 - 0.013ms returns 0
T8CE4 000:304.011 JLINK_GetDebugInfo(0x105 = JLINKARM_DEBUG_INFO_DWT_ADDR_INDEX)
T8CE4 000:304.016   Value=0x********
T8CE4 000:304.024 - 0.013ms returns 0
T8CE4 000:304.030 JLINK_GetDebugInfo(0x106 = JLINKARM_DEBUG_INFO_FPB_ADDR_INDEX)
T8CE4 000:304.035   Value=0x********
T8CE4 000:304.043 - 0.013ms returns 0
T8CE4 000:304.049 JLINK_GetDebugInfo(0x107 = JLINKARM_DEBUG_INFO_NVIC_ADDR_INDEX)
T8CE4 000:304.054   Value=0xE000E000
T8CE4 000:304.061 - 0.012ms returns 0
T8CE4 000:304.067 JLINK_GetDebugInfo(0x10C = JLINKARM_DEBUG_INFO_DBG_ADDR_INDEX)
T8CE4 000:304.075   Value=0xE000EDF0
T8CE4 000:304.085 - 0.017ms returns 0
T8CE4 000:304.091 JLINK_GetDebugInfo(0x01 = Unknown)
T8CE4 000:304.096   Value=0x00000000
T8CE4 000:304.104 - 0.013ms returns 0
T8CE4 000:304.110 JLINK_ReadMemU32(0xE000ED00, 0x1 Items)
T8CE4 000:304.121   CPU_ReadMem(4 bytes @ 0xE000ED00)
T8CE4 000:304.567   Data:  01 C6 0C 41
T8CE4 000:304.584   Debug reg: CPUID
T8CE4 000:304.593 - 0.483ms returns 1 (0x1)
T8CE4 000:304.601 JLINK_GetDebugInfo(0x10F = JLINKARM_DEBUG_INFO_HAS_CORTEX_M_SECURITY_EXT_INDEX)
T8CE4 000:304.607   Value=0x00000000
T8CE4 000:304.615 - 0.014ms returns 0
T8CE4 000:304.622 JLINK_HasError()
T8CE4 000:304.630 JLINK_SetResetType(JLINKARM_CM3_RESET_TYPE_NORMAL)
T8CE4 000:304.636 - 0.006ms returns JLINKARM_CM3_RESET_TYPE_NORMAL
T8CE4 000:304.642 JLINK_Reset()
T8CE4 000:309.485   ResetTarget() start
T8CE4 000:309.509    J-Link Script File: Executing ResetTarget()
T8CE4 000:309.522   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T8CE4 000:310.017   CPU is running
T8CE4 000:310.074   CPU_WriteMem(4 bytes @ 0xE000EDF0)
T8CE4 000:310.534   CPU_ReadMem(4 bytes @ 0x400B0300)
T8CE4 000:310.952   CPU_ReadMem(4 bytes @ 0xE000EDFC)
T8CE4 000:311.394   CPU is running
T8CE4 000:311.408   CPU_WriteMem(4 bytes @ 0xE000EDFC)
T8CE4 000:311.853   CPU is running
T8CE4 000:311.867   CPU_WriteMem(4 bytes @ 0x400B0300)
T8CE4 000:312.301   CPU is running
T8CE4 000:312.320   CPU_WriteMem(4 bytes @ 0x400B0304)
T8CE4 000:312.817   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T8CE4 000:315.734   DAP initialized successfully.
T8CE4 000:315.756   CPU is running
T8CE4 000:315.766   CPU_WriteMem(4 bytes @ 0xE000EDFC)
T8CE4 000:317.547   ResetTarget() end - Took 6.77ms
T8CE4 000:318.910   Device specific reset executed.
T8CE4 000:323.640   CPU_WriteMem(4 bytes @ 0x********)
T8CE4 000:324.135   CPU_ReadMem(4 bytes @ 0xE000EDFC)
T8CE4 000:324.627   CPU_ReadMem(4 bytes @ 0x********)
T8CE4 000:325.174   CPU_WriteMem(4 bytes @ 0x********)
T8CE4 000:325.656 - 21.013ms 
T8CE4 000:325.669 JLINK_Halt()
T8CE4 000:325.675 - 0.006ms returns 0x00
T8CE4 000:325.682 JLINK_ReadMemU32(0xE000EDF0, 0x1 Items)
T8CE4 000:325.692   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T8CE4 000:326.153   Data:  03 00 03 00
T8CE4 000:326.164   Debug reg: DHCSR
T8CE4 000:326.206 - 0.520ms returns 1 (0x1)
T8CE4 000:326.214 JLINK_WriteU32(0xE000EDF0, 0xA05F0003)
T8CE4 000:326.219   Debug reg: DHCSR
T8CE4 000:326.439   CPU_WriteMem(4 bytes @ 0xE000EDF0)
T8CE4 000:326.922 - 0.706ms returns 0 (0x00000000)
T8CE4 000:326.968 JLINK_WriteU32(0xE000EDFC, 0x01000000)
T8CE4 000:327.000   Debug reg: DEMCR
T8CE4 000:327.055   CPU_WriteMem(4 bytes @ 0xE000EDFC)
T8CE4 000:327.684 - 0.717ms returns 0 (0x00000000)
T8CE4 000:332.700 JLINK_GetHWStatus(...)
T8CE4 000:332.918 - 0.217ms returns 0
T8CE4 000:338.138 JLINK_GetNumBPUnits(Type = 0xFFFFFF00)
T8CE4 000:338.165 - 0.026ms returns 0x04
T8CE4 000:338.172 JLINK_GetNumBPUnits(Type = 0xF0)
T8CE4 000:338.178 - 0.006ms returns 0x2000
T8CE4 000:338.185 JLINK_GetNumWPUnits()
T8CE4 000:338.191 - 0.005ms returns 2
T8CE4 000:341.580 JLINK_GetSpeed()
T8CE4 000:341.600 - 0.019ms returns 2000
T8CE4 000:344.338 JLINK_ReadMemU32(0xE000E004, 0x1 Items)
T8CE4 000:344.368   CPU_ReadMem(4 bytes @ 0xE000E004)
T8CE4 000:344.833   Data:  00 00 00 00
T8CE4 000:344.852 - 0.514ms returns 1 (0x1)
T8CE4 000:344.864 JLINK_Halt()
T8CE4 000:344.870 - 0.005ms returns 0x00
T8CE4 000:344.876 JLINK_IsHalted()
T8CE4 000:344.883 - 0.006ms returns TRUE
T2760 073:089.096   
  ***** Error: Connection to emulator lost!
T8CE4 203:214.688 JLINK_Close()
T8CE4 203:232.272 - 17.576ms
T8CE4 203:232.320   
T8CE4 203:232.320   Closed
