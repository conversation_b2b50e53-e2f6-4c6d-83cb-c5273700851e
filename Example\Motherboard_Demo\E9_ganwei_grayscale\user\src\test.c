/*********************************************************************************************************************
* 感为8路灰度传感器集成测试程序
* 
* 本程序用于验证感为8路灰度传感器在逐飞MSPM0G3507开源库中的集成效果
* 
* 测试功能：
* 1. 传感器初始化测试
* 2. 原始ADC数据采集测试
* 3. 校准功能测试
* 4. 数字量输出测试
* 5. 归一化数据测试
* 6. 性能测试
* 
* 使用方法：
* 1. 将此文件复制到Example/Motherboard_Demo/E9_ganwei_grayscale/user/src/目录下
* 2. 替换原有的main.c文件
* 3. 编译并下载到开发板
* 4. 通过串口助手查看测试结果
********************************************************************************************************************/

#include "zf_common_headfile.h"

// 测试配置
#define TEST_CYCLES                 100     // 测试循环次数
#define CALIBRATION_SAMPLES         20      // 校准采样次数
#define PERFORMANCE_TEST_TIME       10000   // 性能测试时间(ms)

// 感为8路灰度传感器对象
ganwei_grayscale_info_struct grayscale_sensor;

// 测试数据存储
uint16 analog_data[8];              // 模拟量数据
uint16 normalized_data[8];          // 归一化数据
uint8 digital_data;                 // 数字量数据

// 校准数据（测试用默认值）
uint16 test_white_values[8] = {1800, 1800, 1800, 1800, 1800, 1800, 1800, 1800};
uint16 test_black_values[8] = {300, 300, 300, 300, 300, 300, 300, 300};

// 测试结果统计
uint32 test_success_count = 0;
uint32 test_fail_count = 0;

//-------------------------------------------------------------------------------------------------------------------
// 函数简介     打印测试结果
// 参数说明     test_name       测试名称
// 参数说明     result          测试结果 0-失败 1-成功
// 返回参数     void
//-------------------------------------------------------------------------------------------------------------------
void print_test_result(const char* test_name, uint8 result)
{
    if(result)
    {
        printf("[PASS] %s\r\n", test_name);
        test_success_count++;
    }
    else
    {
        printf("[FAIL] %s\r\n", test_name);
        test_fail_count++;
    }
}

//-------------------------------------------------------------------------------------------------------------------
// 函数简介     传感器初始化测试
// 返回参数     uint8           测试结果 0-失败 1-成功
//-------------------------------------------------------------------------------------------------------------------
uint8 test_sensor_init(void)
{
    printf("\n=== 传感器初始化测试 ===\r\n");
    
    // 测试基础初始化
    uint8 result = ganwei_grayscale_init(&grayscale_sensor, 
                                        GANWEI_GRAYSCALE_CLASS_EDITION,
                                        GANWEI_GRAYSCALE_ADC_12BITS,
                                        A0, A1, A2,
                                        ADC_IN_CH0);
    
    if(!result)
    {
        printf("基础初始化失败\r\n");
        return 0;
    }
    
    // 检查初始化参数
    if(grayscale_sensor.edition != GANWEI_GRAYSCALE_CLASS_EDITION)
    {
        printf("传感器版本设置错误\r\n");
        return 0;
    }
    
    if(grayscale_sensor.adc_resolution != GANWEI_GRAYSCALE_ADC_12BITS)
    {
        printf("ADC分辨率设置错误\r\n");
        return 0;
    }
    
    if(grayscale_sensor.adc_max_value != 4095.0f)
    {
        printf("ADC最大值设置错误\r\n");
        return 0;
    }
    
    printf("基础初始化成功\r\n");
    printf("传感器版本: %s\r\n", (grayscale_sensor.edition == GANWEI_GRAYSCALE_CLASS_EDITION) ? "经典版" : "青春版");
    printf("ADC分辨率: %d位\r\n", (grayscale_sensor.adc_resolution == GANWEI_GRAYSCALE_ADC_12BITS) ? 12 : 8);
    printf("ADC最大值: %.0f\r\n", grayscale_sensor.adc_max_value);
    
    return 1;
}

//-------------------------------------------------------------------------------------------------------------------
// 函数简介     ADC数据采集测试
// 返回参数     uint8           测试结果 0-失败 1-成功
//-------------------------------------------------------------------------------------------------------------------
uint8 test_adc_sampling(void)
{
    printf("\n=== ADC数据采集测试 ===\r\n");
    
    uint8 i, j;
    uint16 sample_data[8];
    uint8 valid_channels = 0;
    
    // 进行多次采样测试
    for(i = 0; i < 5; i++)
    {
        ganwei_grayscale_task(&grayscale_sensor);
        ganwei_grayscale_get_analog(&grayscale_sensor, sample_data);
        
        printf("采样%d: ", i + 1);
        for(j = 0; j < 8; j++)
        {
            printf("%d ", sample_data[j]);
        }
        printf("\r\n");
        
        system_delay_ms(100);
    }
    
    // 检查数据有效性
    for(j = 0; j < 8; j++)
    {
        if(sample_data[j] > 0 && sample_data[j] <= 4095)
        {
            valid_channels++;
        }
    }
    
    printf("有效通道数: %d/8\r\n", valid_channels);
    
    return (valid_channels >= 4) ? 1 : 0;  // 至少4个通道有效才算成功
}

//-------------------------------------------------------------------------------------------------------------------
// 函数简介     校准功能测试
// 返回参数     uint8           测试结果 0-失败 1-成功
//-------------------------------------------------------------------------------------------------------------------
uint8 test_calibration(void)
{
    printf("\n=== 校准功能测试 ===\r\n");
    
    // 使用测试校准数据
    uint8 result = ganwei_grayscale_init_with_calibration(&grayscale_sensor, test_white_values, test_black_values);
    
    if(!result)
    {
        printf("校准初始化失败\r\n");
        return 0;
    }
    
    // 检查校准标志
    if(!grayscale_sensor.init_flag)
    {
        printf("校准标志未设置\r\n");
        return 0;
    }
    
    // 检查校准数据
    uint8 i;
    for(i = 0; i < 8; i++)
    {
        if(grayscale_sensor.calibrated_white[i] != test_white_values[i] ||
           grayscale_sensor.calibrated_black[i] != test_black_values[i])
        {
            printf("校准数据设置错误，通道%d\r\n", i);
            return 0;
        }
    }
    
    printf("校准数据设置成功\r\n");
    printf("白值: ");
    for(i = 0; i < 8; i++)
    {
        printf("%d ", grayscale_sensor.calibrated_white[i]);
    }
    printf("\r\n");
    
    printf("黑值: ");
    for(i = 0; i < 8; i++)
    {
        printf("%d ", grayscale_sensor.calibrated_black[i]);
    }
    printf("\r\n");
    
    return 1;
}

//-------------------------------------------------------------------------------------------------------------------
// 函数简介     数字量输出测试
// 返回参数     uint8           测试结果 0-失败 1-成功
//-------------------------------------------------------------------------------------------------------------------
uint8 test_digital_output(void)
{
    printf("\n=== 数字量输出测试 ===\r\n");
    
    uint8 i;
    uint8 digital_valid = 0;
    
    // 进行多次数字量测试
    for(i = 0; i < 10; i++)
    {
        ganwei_grayscale_task(&grayscale_sensor);
        digital_data = ganwei_grayscale_get_digital(&grayscale_sensor);
        
        printf("数字量%d: ", i + 1);
        for(uint8 j = 0; j < 8; j++)
        {
            printf("%d", (digital_data >> j) & 0x01);
        }
        printf(" (0x%02X)\r\n", digital_data);
        
        // 检查数字量是否有变化（说明传感器在工作）
        if(digital_data != 0x00 && digital_data != 0xFF)
        {
            digital_valid = 1;
        }
        
        system_delay_ms(100);
    }
    
    return digital_valid;
}

//-------------------------------------------------------------------------------------------------------------------
// 函数简介     归一化数据测试
// 返回参数     uint8           测试结果 0-失败 1-成功
//-------------------------------------------------------------------------------------------------------------------
uint8 test_normalized_output(void)
{
    printf("\n=== 归一化数据测试 ===\r\n");
    
    uint8 i, j;
    uint8 normalized_valid = 0;
    
    // 进行多次归一化测试
    for(i = 0; i < 5; i++)
    {
        ganwei_grayscale_task(&grayscale_sensor);
        
        if(ganwei_grayscale_get_normalized(&grayscale_sensor, normalized_data))
        {
            printf("归一化%d: ", i + 1);
            for(j = 0; j < 8; j++)
            {
                printf("%d ", normalized_data[j]);
            }
            printf("\r\n");
            
            // 检查归一化数据范围
            for(j = 0; j < 8; j++)
            {
                if(normalized_data[j] <= 4095)
                {
                    normalized_valid = 1;
                }
            }
        }
        else
        {
            printf("归一化数据获取失败\r\n");
            return 0;
        }
        
        system_delay_ms(100);
    }
    
    return normalized_valid;
}

//-------------------------------------------------------------------------------------------------------------------
// 函数简介     性能测试
// 返回参数     uint8           测试结果 0-失败 1-成功
//-------------------------------------------------------------------------------------------------------------------
uint8 test_performance(void)
{
    printf("\n=== 性能测试 ===\r\n");
    
    uint32 start_time = system_delay_ms(0);  // 获取当前时间
    uint32 cycle_count = 0;
    uint32 elapsed_time;
    
    printf("开始性能测试，持续时间: %dms\r\n", PERFORMANCE_TEST_TIME);
    
    // 性能测试循环
    while(1)
    {
        ganwei_grayscale_task(&grayscale_sensor);
        cycle_count++;
        
        elapsed_time = system_delay_ms(0) - start_time;
        if(elapsed_time >= PERFORMANCE_TEST_TIME)
        {
            break;
        }
        
        system_delay_ms(1);  // 经典版建议1ms延时
    }
    
    float frequency = (float)cycle_count * 1000.0f / (float)elapsed_time;
    
    printf("性能测试结果:\r\n");
    printf("总循环次数: %d\r\n", cycle_count);
    printf("实际耗时: %dms\r\n", elapsed_time);
    printf("实际频率: %.2fHz\r\n", frequency);
    
    // 经典版理论频率1kHz，实际应该接近这个值
    if(grayscale_sensor.edition == GANWEI_GRAYSCALE_CLASS_EDITION)
    {
        return (frequency >= 500.0f) ? 1 : 0;  // 至少达到500Hz才算合格
    }
    else
    {
        return (frequency >= 50.0f) ? 1 : 0;   // 青春版至少50Hz
    }
}

//-------------------------------------------------------------------------------------------------------------------
// 函数简介     综合功能测试
// 返回参数     uint8           测试结果 0-失败 1-成功
//-------------------------------------------------------------------------------------------------------------------
uint8 test_comprehensive(void)
{
    printf("\n=== 综合功能测试 ===\r\n");
    
    uint8 i;
    uint8 all_functions_ok = 1;
    
    // 连续运行所有功能
    for(i = 0; i < 20; i++)
    {
        // 执行传感器任务
        ganwei_grayscale_task(&grayscale_sensor);
        
        // 获取所有类型数据
        digital_data = ganwei_grayscale_get_digital(&grayscale_sensor);
        ganwei_grayscale_get_analog(&grayscale_sensor, analog_data);
        
        if(!ganwei_grayscale_get_normalized(&grayscale_sensor, normalized_data))
        {
            all_functions_ok = 0;
            break;
        }
        
        // 每5次打印一次结果
        if(i % 5 == 0)
        {
            printf("综合测试%d: 数字=%02X, 模拟=%d,%d,%d,%d, 归一化=%d,%d,%d,%d\r\n",
                   i + 1, digital_data,
                   analog_data[0], analog_data[1], analog_data[2], analog_data[3],
                   normalized_data[0], normalized_data[1], normalized_data[2], normalized_data[3]);
        }
        
        system_delay_ms(50);
    }
    
    return all_functions_ok;
}

//-------------------------------------------------------------------------------------------------------------------
// 函数简介     主函数
//-------------------------------------------------------------------------------------------------------------------
int main(void)
{
    clock_init(SYSTEM_CLOCK_80M);   // 时钟配置及系统初始化<务必保留>
    debug_init();                   // 调试串口信息初始化
    
    printf("\r\n");
    printf("========================================\r\n");
    printf("  感为8路灰度传感器集成测试程序\r\n");
    printf("========================================\r\n");
    printf("测试平台: MSPM0G3507\r\n");
    printf("开源库版本: 逐飞科技\r\n");
    printf("传感器型号: 感为无MCU8路灰度传感器\r\n");
    printf("========================================\r\n");
    
    // 初始化测试统计
    test_success_count = 0;
    test_fail_count = 0;
    
    // 执行各项测试
    print_test_result("传感器初始化测试", test_sensor_init());
    system_delay_ms(500);
    
    print_test_result("ADC数据采集测试", test_adc_sampling());
    system_delay_ms(500);
    
    print_test_result("校准功能测试", test_calibration());
    system_delay_ms(500);
    
    print_test_result("数字量输出测试", test_digital_output());
    system_delay_ms(500);
    
    print_test_result("归一化数据测试", test_normalized_output());
    system_delay_ms(500);
    
    print_test_result("性能测试", test_performance());
    system_delay_ms(500);
    
    print_test_result("综合功能测试", test_comprehensive());
    
    // 打印测试总结
    printf("\r\n");
    printf("========================================\r\n");
    printf("           测试结果总结\r\n");
    printf("========================================\r\n");
    printf("成功测试: %d\r\n", test_success_count);
    printf("失败测试: %d\r\n", test_fail_count);
    printf("总测试数: %d\r\n", test_success_count + test_fail_count);
    printf("成功率: %.1f%%\r\n", (float)test_success_count * 100.0f / (float)(test_success_count + test_fail_count));
    
    if(test_fail_count == 0)
    {
        printf("状态: 所有测试通过！集成成功！\r\n");
    }
    else
    {
        printf("状态: 部分测试失败，请检查硬件连接和配置\r\n");
    }
    printf("========================================\r\n");
    
    // 进入正常工作模式
    printf("\r\n进入正常工作模式...\r\n");
    
    while(true)
    {
        // 正常工作循环
        ganwei_grayscale_task(&grayscale_sensor);
        
        // 获取并显示传感器数据
        digital_data = ganwei_grayscale_get_digital(&grayscale_sensor);
        ganwei_grayscale_get_analog(&grayscale_sensor, analog_data);
        ganwei_grayscale_get_normalized(&grayscale_sensor, normalized_data);
        
        // 每秒打印一次数据
        static uint32 last_print_time = 0;
        if(system_delay_ms(0) - last_print_time >= 1000)
        {
            printf("工作数据 - 数字:%02X 模拟:%d,%d,%d,%d 归一化:%d,%d,%d,%d\r\n",
                   digital_data,
                   analog_data[0], analog_data[1], analog_data[2], analog_data[3],
                   normalized_data[0], normalized_data[1], normalized_data[2], normalized_data[3]);
            last_print_time = system_delay_ms(0);
        }
        
        system_delay_ms(1);  // 经典版1ms延时
    }
}