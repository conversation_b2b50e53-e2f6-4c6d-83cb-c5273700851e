
let Common = system.getScript("/ti/driverlib/Common.js");
const StackSizeOptions = {
    "MSPM0G1X0X_G3X0X": {
        "MSPM0G1105":512,
        "MSPM0G1106":512,
        "MSPM0G1107":512,
        "MSPM0G1505":512,
        "MSPM0G1506":512,
        "MSPM0G1507":512,
        "MSPM0G3105":512,
        "MSPM0G3106":512,
        "MSPM0G3107":512,
        "MSPM0G3505":512,
        "MSPM0G3506":512,
        "MSPM0G3507":512,
    },
    "MSPM0L11XX_L13XX": {
        "MSPM0L1105": 256,
        "MSPM0L1106": 256,
        "MSPM0L1303": 256,
        "MSPM0L1304": 256,
        "MSPM0L1305": 256,
        "MSPM0L1306": 256,
        "MSPM0L1343": 256,
        "MSPM0L1344": 256,
        "MSPM0L1345": 256,
        "MSPM0L1346": 256,
    },
    "MSPM0L122X_L222X": {
        "MSPM0L1226":256,
        "MSPM0L1227":256,
        "MSPM0L1228":256,
        "MSPM0L2226":256,
        "MSPM0L2227":256,
        "MSPM0L2228":256,
    },
    "MSPM0C110X": {
        "MSPM0C1103"    :64,
        "MSPM0C1104"    :64,
        "MSPS003F3"     :64,
        "MSPS003F4"     :64,
    },
};
const FLASHOriginOptions = {
    "MSPM0G1X0X_G3X0X": {
        "MSPM0G1105":"0x00000000",
        "MSPM0G1106":"0x00000000",
        "MSPM0G1107":"0x00000000",
        "MSPM0G1505":"0x00000000",
        "MSPM0G1506":"0x00000000",
        "MSPM0G1507":"0x00000000",
        "MSPM0G3105":"0x00000000",
        "MSPM0G3106":"0x00000000",
        "MSPM0G3107":"0x00000000",
        "MSPM0G3505":"0x00000000",
        "MSPM0G3506":"0x00000000",
        "MSPM0G3507":"0x00000000",
    },
    "MSPM0L11XX_L13XX": {
        "MSPM0L1105": "0x00000000",
        "MSPM0L1106": "0x00000000",
        "MSPM0L1303": "0x00000000",
        "MSPM0L1304": "0x00000000",
        "MSPM0L1305": "0x00000000",
        "MSPM0L1306": "0x00000000",
        "MSPM0L1343": "0x00000000",
        "MSPM0L1344": "0x00000000",
        "MSPM0L1345": "0x00000000",
        "MSPM0L1346": "0x00000000",
    },
    "MSPM0L122X_L222X": {
        "MSPM0L1226": "0x00000000",
        "MSPM0L1227": "0x00000000",
        "MSPM0L1228": "0x00000000",
        "MSPM0L2226": "0x00000000",
        "MSPM0L2227": "0x00000000",
        "MSPM0L2228": "0x00000000",
    },
    "MSPM0C110X": {
        "MSPM0C1103"    :"0x00000000",
        "MSPM0C1104"    :"0x00000000",
        "MSPS003F3"     :"0x00000000",
        "MSPS003F4"     :"0x00000000",
    },
}

const FLASHLengthOptions = {
    "MSPM0G1X0X_G3X0X": {
        "MSPM0G1105":"0x00008000",
        "MSPM0G1106":"0x00010000",
        "MSPM0G1107":"0x00020000",
        "MSPM0G1505":"0x00008000",
        "MSPM0G1506":"0x00010000",
        "MSPM0G1507":"0x00020000",
        "MSPM0G3105":"0x00008000",
        "MSPM0G3106":"0x00010000",
        "MSPM0G3107":"0x00020000",
        "MSPM0G3505":"0x00008000",
        "MSPM0G3506":"0x00010000",
        "MSPM0G3507":"0x00020000",
    },
    "MSPM0L11XX_L13XX": {
        "MSPM0L1105": "0x00007FF8",
        "MSPM0L1106": "0x0000FFF8",
        "MSPM0L1303": "0x00001FF8",
        "MSPM0L1304": "0x00003FF8",
        "MSPM0L1305": "0x00007FF8",
        "MSPM0L1306": "0x0000FFF8",
        "MSPM0L1343": "0x00001FF8",
        "MSPM0L1344": "0x00003FF8",
        "MSPM0L1345": "0x00007FF8",
        "MSPM0L1346": "0x0000FFF8",
    },
    "MSPM0L122X_L222X": {
        "MSPM0L1226": "0x00010000",
        "MSPM0L1227": "0x00020000",
        "MSPM0L1228": "0x00040000",
        "MSPM0L2226": "0x00010000",
        "MSPM0L2227": "0x00020000",
        "MSPM0L2228": "0x00040000",
    },
    "MSPM0C110X": {
        "MSPM0C1103"    :"0x00002000",
        "MSPM0C1104"    :"0x00004000",
        "MSPS003F3"     :"0x00002000",
        "MSPS003F4"     :"0x00004000",
    },
};
const SRAMOriginOptions = {
    "MSPM0G1X0X_G3X0X": {
        "MSPM0G1105":"0x20200000",
        "MSPM0G1106":"0x20200000",
        "MSPM0G1107":"0x20200000",
        "MSPM0G1505":"0x20200000",
        "MSPM0G1506":"0x20200000",
        "MSPM0G1507":"0x20200000",
        "MSPM0G3105":"0x20200000",
        "MSPM0G3106":"0x20200000",
        "MSPM0G3107":"0x20200000",
        "MSPM0G3505":"0x20200000",
        "MSPM0G3506":"0x20200000",
        "MSPM0G3507":"0x20200000",
    },
    "MSPM0L11XX_L13XX": {
        "MSPM0L1105": "0x20000000",
        "MSPM0L1106": "0x20000000",
        "MSPM0L1303": "0x20000000",
        "MSPM0L1304": "0x20000000",
        "MSPM0L1305": "0x20000000",
        "MSPM0L1306": "0x20000000",
        "MSPM0L1343": "0x20000000",
        "MSPM0L1344": "0x20000000",
        "MSPM0L1345": "0x20000000",
        "MSPM0L1346": "0x20000000",
    },
    "MSPM0L122X_L222X": {
        "MSPM0L1226": "0x20200000",
        "MSPM0L1227": "0x20200000",
        "MSPM0L1228": "0x20200000",
        "MSPM0L2226": "0x20200000",
        "MSPM0L2227": "0x20200000",
        "MSPM0L2228": "0x20200000",
    },
    "MSPM0C110X": {
        "MSPM0C1103"    :"0x20000000",
        "MSPM0C1104"    :"0x20000000",
        "MSPS003F3"     :"0x20000000",
        "MSPS003F4"     :"0x20000000",
    },
};
const SRAMLengthOptions = {
    "MSPM0G1X0X_G3X0X": {
        "MSPM0G1105": "0x00004000",
        "MSPM0G1106": "0x00008000",
        "MSPM0G1107": "0x00008000",
        "MSPM0G1505": "0x00004000",
        "MSPM0G1506": "0x00008000",
        "MSPM0G1507": "0x00008000",
        "MSPM0G3105": "0x00004000",
        "MSPM0G3106": "0x00008000",
        "MSPM0G3107": "0x00008000",
        "MSPM0G3505": "0x00004000",
        "MSPM0G3506": "0x00008000",
        "MSPM0G3507": "0x00008000",
    },
    "MSPM0L11XX_L13XX": {
        "MSPM0L1105": "0x00001000",
        "MSPM0L1106": "0x00001000",
        "MSPM0L1303": "0x00000800",
        "MSPM0L1304": "0x00000800",
        "MSPM0L1305": "0x00001000",
        "MSPM0L1306": "0x00001000",
        "MSPM0L1343": "0x00000800",
        "MSPM0L1344": "0x00000800",
        "MSPM0L1345": "0x00001000",
        "MSPM0L1346": "0x00001000",
    },
    "MSPM0L122X_L222X": {
        "MSPM0L1226": "0x00008000",
        "MSPM0L1227": "0x00008000",
        "MSPM0L1228": "0x00008000",
        "MSPM0L2226": "0x00008000",
        "MSPM0L2227": "0x00008000",
        "MSPM0L2228": "0x00008000",
    },
    "MSPM0C110X": {
        "MSPM0C1103"    : "0x00000400",
        "MSPM0C1104"    : "0x00000400",
        "MSPS003F3"     : "0x00000400",
        "MSPS003F4"     : "0x00000400",
    },
};
const BCROriginOptions = {
    "MSPM0G1X0X_G3X0X": {
        "MSPM0G1105":"0x41C00000",
        "MSPM0G1106":"0x41C00000",
        "MSPM0G1107":"0x41C00000",
        "MSPM0G1505":"0x41C00000",
        "MSPM0G1506":"0x41C00000",
        "MSPM0G1507":"0x41C00000",
        "MSPM0G3105":"0x41C00000",
        "MSPM0G3106":"0x41C00000",
        "MSPM0G3107":"0x41C00000",
        "MSPM0G3505":"0x41C00000",
        "MSPM0G3506":"0x41C00000",
        "MSPM0G3507":"0x41C00000",
    },
    "MSPM0L11XX_L13XX": {
        "MSPM0L1105": "0x41C00000",
        "MSPM0L1106": "0x41C00000",
        "MSPM0L1303": "0x41C00000",
        "MSPM0L1304": "0x41C00000",
        "MSPM0L1305": "0x41C00000",
        "MSPM0L1306": "0x41C00000",
        "MSPM0L1343": "0x41C00000",
        "MSPM0L1344": "0x41C00000",
        "MSPM0L1345": "0x41C00000",
        "MSPM0L1346": "0x41C00000",
    },
    "MSPM0L122X_L222X": {
        "MSPM0L1226": "0x41C00000",
        "MSPM0L1227": "0x41C00000",
        "MSPM0L1228": "0x41C00000",
        "MSPM0L2226": "0x41C00000",
        "MSPM0L2227": "0x41C00000",
        "MSPM0L2228": "0x41C00000",
    },
    "MSPM0C110X": {
        "MSPM0C1103"    :"0x41C00000",
        "MSPM0C1104"    :"0x41C00000",
        "MSPS003F3"     :"0x41C00000",
        "MSPS003F4"     :"0x41C00000",
    },
};
const BCRLengthOptions = {
    "MSPM0G1X0X_G3X0X": {
        "MSPM0G1105": "0x00000080",
        "MSPM0G1106": "0x00000080",
        "MSPM0G1107": "0x00000080",
        "MSPM0G1505": "0x00000080",
        "MSPM0G1506": "0x00000080",
        "MSPM0G1507": "0x00000080",
        "MSPM0G3105": "0x00000080",
        "MSPM0G3106": "0x00000080",
        "MSPM0G3107": "0x00000080",
        "MSPM0G3505": "0x00000080",
        "MSPM0G3506": "0x00000080",
        "MSPM0G3507": "0x00000080",
    },
    "MSPM0L11XX_L13XX": {
        "MSPM0L1105": "0x00000080",
        "MSPM0L1106": "0x00000080",
        "MSPM0L1303": "0x00000080",
        "MSPM0L1304": "0x00000080",
        "MSPM0L1305": "0x00000080",
        "MSPM0L1306": "0x00000080",
        "MSPM0L1343": "0x00000080",
        "MSPM0L1344": "0x00000080",
        "MSPM0L1345": "0x00000080",
        "MSPM0L1346": "0x00000080",
    },
    "MSPM0L122X_L222X": {
        "MSPM0L1226": "0x000000FF",
        "MSPM0L1227": "0x000000FF",
        "MSPM0L1228": "0x000000FF",
        "MSPM0L2226": "0x000000FF",
        "MSPM0L2227": "0x000000FF",
        "MSPM0L2228": "0x000000FF",
    },
    "MSPM0C110X": {
        "MSPM0C1103"    : "0x00000080",
        "MSPM0C1104"    : "0x00000080",
        "MSPS003F3"     : "0x00000080",
        "MSPS003F4"     : "0x00000080",
    },
};
const BSLOriginOptions = {
    "MSPM0G1X0X_G3X0X": {
        "MSPM0G1105":"0x41C00100",
        "MSPM0G1106":"0x41C00100",
        "MSPM0G1107":"0x41C00100",
        "MSPM0G1505":"0x41C00100",
        "MSPM0G1506":"0x41C00100",
        "MSPM0G1507":"0x41C00100",
        "MSPM0G3105":"0x41C00100",
        "MSPM0G3106":"0x41C00100",
        "MSPM0G3107":"0x41C00100",
        "MSPM0G3505":"0x41C00100",
        "MSPM0G3506":"0x41C00100",
        "MSPM0G3507":"0x41C00100",
    },
    "MSPM0L11XX_L13XX": {
        "MSPM0L1105": "0x41C00100",
        "MSPM0L1106": "0x41C00100",
        "MSPM0L1303": "0x41C00100",
        "MSPM0L1304": "0x41C00100",
        "MSPM0L1305": "0x41C00100",
        "MSPM0L1306": "0x41C00100",
        "MSPM0L1343": "0x41C00100",
        "MSPM0L1344": "0x41C00100",
        "MSPM0L1345": "0x41C00100",
        "MSPM0L1346": "0x41C00100",
    },
    "MSPM0L122X_L222X": {
        "MSPM0L1226": "0x41C00100",
        "MSPM0L1227": "0x41C00100",
        "MSPM0L1228": "0x41C00100",
        "MSPM0L2226": "0x41C00100",
        "MSPM0L2227": "0x41C00100",
        "MSPM0L2228": "0x41C00100",
    },
    "MSPM0C110X": {
        "MSPM0C1103"    :undefined,
        "MSPM0C1104"    :undefined,
        "MSPS003F3"     :undefined,
        "MSPS003F4"     :undefined,
    },
};
const BSLLengthOptions = {
    "MSPM0G1X0X_G3X0X": {
        "MSPM0G1105":"0x00000080",
        "MSPM0G1106":"0x00000080",
        "MSPM0G1107":"0x00000080",
        "MSPM0G1505":"0x00000080",
        "MSPM0G1506":"0x00000080",
        "MSPM0G1507":"0x00000080",
        "MSPM0G3105":"0x00000080",
        "MSPM0G3106":"0x00000080",
        "MSPM0G3107":"0x00000080",
        "MSPM0G3505":"0x00000080",
        "MSPM0G3506":"0x00000080",
        "MSPM0G3507":"0x00000080",
    },
    "MSPM0L11XX_L13XX": {
        "MSPM0L1105": "0x00000080",
        "MSPM0L1106": "0x00000080",
        "MSPM0L1303": "0x00000080",
        "MSPM0L1304": "0x00000080",
        "MSPM0L1305": "0x00000080",
        "MSPM0L1306": "0x00000080",
        "MSPM0L1343": "0x00000080",
        "MSPM0L1344": "0x00000080",
        "MSPM0L1345": "0x00000080",
        "MSPM0L1346": "0x00000080",
    },
    "MSPM0L122X_L222X": {
        "MSPM0L1226": "0x00000080",
        "MSPM0L1227": "0x00000080",
        "MSPM0L1228": "0x00000080",
        "MSPM0L2226": "0x00000080",
        "MSPM0L2227": "0x00000080",
        "MSPM0L2228": "0x00000080",
    },
    "MSPM0C110X": {
        "MSPM0C1103"    :undefined,
        "MSPM0C1104"    :undefined,
        "MSPS003F3"     :undefined,
        "MSPS003F4"     :undefined,
    },
};


let index = Common.getDeviceFamily();

exports = {
    StackSize   : StackSizeOptions[index],
    FLASHOrigin : FLASHOriginOptions[index],
    FLASHLength : FLASHLengthOptions[index],
    SRAMOrigin  : SRAMOriginOptions[index],
    SRAMLength  : SRAMLengthOptions[index],
    BCROrigin   : BCROriginOptions[index],
    BCRLength   : BCRLengthOptions[index],
    BSLOrigin   : BSLOriginOptions[index],
    BSLLength   : BSLLengthOptions[index],
}
