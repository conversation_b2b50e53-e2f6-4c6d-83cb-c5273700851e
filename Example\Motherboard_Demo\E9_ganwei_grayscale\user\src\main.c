/*********************************************************************************************************************
* MSPM0G3507 Opensource Library 即（MSPM0G3507 开源库）是一个基于官方 SDK 接口的第三方开源库
* Copyright (c) 2022 SEEKFREE 逐飞科技
* 
* 本文件是 MSPM0G3507 开源库的一部分
* 
* MSPM0G3507 开源库 是免费软件
* 您可以根据自由软件基金会发布的 GPL（GNU General Public License，即 GNU通用公共许可证）的条款
* 即 GPL 的第3版（即 GPL3.0）或（您选择的）任何后来的版本，重新发布和/或修改它
* 
* 本开源库的发布是希望它能发挥作用，但并未对其作任何的保证
* 甚至没有隐含的适销性或适合特定用途的保证
* 更多细节请参见 GPL
* 
* 您应该在收到本开源库的同时收到一份 GPL 的副本
* 如果没有，请参阅<https://www.gnu.org/licenses/>
* 
* 额外注明：
* 本开源库使用 GPL3.0 开源许可证协议 以上许可申明为译文版本
* 许可申明英文版在 libraries/doc 文件夹下的 GPL3_permission_statement.txt 文件中
* 许可证副本在 libraries 文件夹下 即该文件夹下的 LICENSE 文件
* 欢迎各位使用并传播本程序 但修改内容时必须保留逐飞科技的版权声明（即本声明）
* 
* 文件名称          main
* 公司名称          成都逐飞科技有限公司
* 版本信息          查看 libraries/doc 文件夹内 version 文件 版本说明
* 开发环境          MDK 5.37
* 适用平台          MSPM0G3507
* 店铺链接          https://seekfree.taobao.com/
* 
* 修改记录
* 日期              作者                备注
* 2025-01-01       SeekFree            感为8路灰度传感器例程
********************************************************************************************************************/

#include "zf_common_headfile.h"

// 打开新的工程或者工程移动了位置务必执行以下操作
// 第一步 关闭上面所有打开的文件
// 第二步 project->clean  等待下方进度条走完

// *************************** 例程测试说明 ***************************
// 1.核心板烧录完成本例程，完成上电
// 
// 2.将感为8路灰度传感器按照以下方式连接：
//   地址线0 -> A0 (PB0)
//   地址线1 -> A1 (PB1) 
//   地址线2 -> A2 (PB2)
//   模拟输出 -> ADC_IN_CH0 (PB0对应的ADC通道)
//   VCC -> 3.3V
//   GND -> GND
// 
// 3.通过串口助手可以看到传感器的模拟量、数字量和归一化数据
// 
// 4.首次运行会显示原始ADC值，可用于黑白校准
//   将传感器放在白色表面记录白值，放在黑色表面记录黑值
//   然后修改代码中的white_values和black_values数组

// 感为8路灰度传感器对象
ganwei_grayscale_info_struct grayscale_sensor;

// 校准数据 - 根据实际测试结果修改这些值
uint16 white_values[8] = {1800, 1800, 1800, 1800, 1800, 1800, 1800, 1800};  // 白色校准值
uint16 black_values[8] = {300, 300, 300, 300, 300, 300, 300, 300};          // 黑色校准值

// 数据存储数组
uint16 analog_data[8];      // 模拟量数据
uint16 normalized_data[8];  // 归一化数据
uint8 digital_data;         // 数字量数据

int main(void)
{
    clock_init(SYSTEM_CLOCK_80M);   // 时钟配置及系统初始化<务必保留>
    debug_init();                   // 调试串口信息初始化
    // 此处编写用户代码 例如外设初始化代码等

    printf("感为8路灰度传感器测试程序\r\n");
    
    // 初始化感为8路灰度传感器
    // 参数：传感器对象，版本(经典版/青春版)，ADC分辨率，地址线引脚，ADC引脚
    ganwei_grayscale_init(&grayscale_sensor,
                         GANWEI_GRAYSCALE_CLASS_EDITION,    // 使用经典版
                         GANWEI_GRAYSCALE_ADC_12BITS,       // 12位ADC
                         A0, A1, A2,                        // 地址线引脚
                         ADC0_CH0_A27);                     // ADC引脚
    
    printf("传感器初始化完成\r\n");
    
    // 延时等待传感器稳定
    system_delay_ms(100);
    
    // 首次运行：获取原始ADC值用于校准
    printf("正在获取原始ADC值用于校准...\r\n");
    for(uint8 i = 0; i < 10; i++)  // 采集10次取平均
    {
        ganwei_grayscale_task(&grayscale_sensor);
        ganwei_grayscale_get_analog(&grayscale_sensor, analog_data);
        
        printf("ADC原始值: %d-%d-%d-%d-%d-%d-%d-%d\r\n", 
               analog_data[0], analog_data[1], analog_data[2], analog_data[3],
               analog_data[4], analog_data[5], analog_data[6], analog_data[7]);
        
        system_delay_ms(100);
    }
    
    printf("请根据上述ADC值修改代码中的校准数据\r\n");
    printf("白色表面对应的值作为white_values，黑色表面对应的值作为black_values\r\n");
    
    // 使用校准数据初始化传感器
    ganwei_grayscale_init_with_calibration(&grayscale_sensor, white_values, black_values);
    printf("校准数据加载完成\r\n");
    
    system_delay_ms(100);
    
    // 此处编写用户代码 例如外设初始化代码等
    while(true)
    {
        // 此处编写需要循环执行的代码
        
        // 执行传感器任务（包含模拟量、数字量、归一化量处理）
        ganwei_grayscale_task(&grayscale_sensor);
        
        // 获取数字量结果
        digital_data = ganwei_grayscale_get_digital(&grayscale_sensor);
        printf("数字量: %d-%d-%d-%d-%d-%d-%d-%d\r\n",
               (digital_data >> 0) & 0x01, (digital_data >> 1) & 0x01,
               (digital_data >> 2) & 0x01, (digital_data >> 3) & 0x01,
               (digital_data >> 4) & 0x01, (digital_data >> 5) & 0x01,
               (digital_data >> 6) & 0x01, (digital_data >> 7) & 0x01);
        
        // 获取模拟量结果
        if(ganwei_grayscale_get_analog(&grayscale_sensor, analog_data))
        {
            printf("模拟量: %d-%d-%d-%d-%d-%d-%d-%d\r\n",
                   analog_data[0], analog_data[1], analog_data[2], analog_data[3],
                   analog_data[4], analog_data[5], analog_data[6], analog_data[7]);
        }
        
        // 获取归一化结果
        if(ganwei_grayscale_get_normalized(&grayscale_sensor, normalized_data))
        {
            printf("归一化: %d-%d-%d-%d-%d-%d-%d-%d\r\n",
                   normalized_data[0], normalized_data[1], normalized_data[2], normalized_data[3],
                   normalized_data[4], normalized_data[5], normalized_data[6], normalized_data[7]);
        }
        
        // 经典版理论性能1kHz，只需要delay 1ms
        // 青春版100Hz，需要delay 10ms，否则不能正常使用
        system_delay_ms(1);  // 经典版使用1ms延时
        
        // 此处编写需要循环执行的代码
    }
}