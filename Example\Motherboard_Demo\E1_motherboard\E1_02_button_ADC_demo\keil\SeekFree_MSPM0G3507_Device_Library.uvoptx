<?xml version="1.0" encoding="UTF-8" standalone="no" ?>
<ProjectOpt xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="project_optx.xsd">

  <SchemaVersion>1.0</SchemaVersion>

  <Header>### uVision Project, (C) Keil Software</Header>

  <Extensions>
    <cExt>*.c</cExt>
    <aExt>*.s*; *.src; *.a*</aExt>
    <oExt>*.obj; *.o</oExt>
    <lExt>*.lib</lExt>
    <tExt>*.txt; *.h; *.inc; *.md</tExt>
    <pExt>*.plm</pExt>
    <CppX>*.cpp; *.cc; *.cxx</CppX>
    <nMigrate>0</nMigrate>
  </Extensions>

  <DaveTm>
    <dwLowDateTime>0</dwLowDateTime>
    <dwHighDateTime>0</dwHighDateTime>
  </DaveTm>

  <Target>
    <TargetName>SeekFree_MSPM0G3507_Device_Library</TargetName>
    <ToolsetNumber>0x4</ToolsetNumber>
    <ToolsetName>ARM-ADS</ToolsetName>
    <TargetOption>
      <CLKADS>12000000</CLKADS>
      <OPTTT>
        <gFlags>1</gFlags>
        <BeepAtEnd>1</BeepAtEnd>
        <RunSim>0</RunSim>
        <RunTarget>1</RunTarget>
        <RunAbUc>0</RunAbUc>
      </OPTTT>
      <OPTHX>
        <HexSelection>1</HexSelection>
        <FlashByte>65535</FlashByte>
        <HexRangeLowAddress>0</HexRangeLowAddress>
        <HexRangeHighAddress>0</HexRangeHighAddress>
        <HexOffset>0</HexOffset>
      </OPTHX>
      <OPTLEX>
        <PageWidth>79</PageWidth>
        <PageLength>66</PageLength>
        <TabStop>8</TabStop>
        <ListingPath>.\Listings\</ListingPath>
      </OPTLEX>
      <ListingPage>
        <CreateCListing>1</CreateCListing>
        <CreateAListing>1</CreateAListing>
        <CreateLListing>1</CreateLListing>
        <CreateIListing>0</CreateIListing>
        <AsmCond>1</AsmCond>
        <AsmSymb>1</AsmSymb>
        <AsmXref>0</AsmXref>
        <CCond>1</CCond>
        <CCode>0</CCode>
        <CListInc>0</CListInc>
        <CSymb>0</CSymb>
        <LinkerCodeListing>0</LinkerCodeListing>
      </ListingPage>
      <OPTXL>
        <LMap>1</LMap>
        <LComments>1</LComments>
        <LGenerateSymbols>1</LGenerateSymbols>
        <LLibSym>1</LLibSym>
        <LLines>1</LLines>
        <LLocSym>1</LLocSym>
        <LPubSym>1</LPubSym>
        <LXref>0</LXref>
        <LExpSel>0</LExpSel>
      </OPTXL>
      <OPTFL>
        <tvExp>1</tvExp>
        <tvExpOptDlg>0</tvExpOptDlg>
        <IsCurrentTarget>1</IsCurrentTarget>
      </OPTFL>
      <CpuCode>4</CpuCode>
      <DebugOpt>
        <uSim>0</uSim>
        <uTrg>1</uTrg>
        <sLdApp>1</sLdApp>
        <sGomain>1</sGomain>
        <sRbreak>1</sRbreak>
        <sRwatch>1</sRwatch>
        <sRmem>1</sRmem>
        <sRfunc>1</sRfunc>
        <sRbox>1</sRbox>
        <tLdApp>1</tLdApp>
        <tGomain>1</tGomain>
        <tRbreak>1</tRbreak>
        <tRwatch>1</tRwatch>
        <tRmem>1</tRmem>
        <tRfunc>0</tRfunc>
        <tRbox>1</tRbox>
        <tRtrace>1</tRtrace>
        <sRSysVw>1</sRSysVw>
        <tRSysVw>1</tRSysVw>
        <sRunDeb>0</sRunDeb>
        <sLrtime>0</sLrtime>
        <bEvRecOn>1</bEvRecOn>
        <bSchkAxf>0</bSchkAxf>
        <bTchkAxf>0</bTchkAxf>
        <nTsel>3</nTsel>
        <sDll></sDll>
        <sDllPa></sDllPa>
        <sDlgDll></sDlgDll>
        <sDlgPa></sDlgPa>
        <sIfile></sIfile>
        <tDll></tDll>
        <tDllPa></tDllPa>
        <tDlgDll></tDlgDll>
        <tDlgPa></tDlgPa>
        <tIfile></tIfile>
        <pMon>BIN\CMSIS_AGDI.dll</pMon>
      </DebugOpt>
      <TargetDriverDllRegistry>
        <SetRegEntry>
          <Number>0</Number>
          <Key>ARMRTXEVENTFLAGS</Key>
          <Name>-L70 -Z18 -C0 -M0 -T1</Name>
        </SetRegEntry>
        <SetRegEntry>
          <Number>0</Number>
          <Key>DLGTARM</Key>
          <Name>(1010=1487,175,1898,677,0)(1007=-1,-1,-1,-1,0)(1008=-1,-1,-1,-1,0)(1012=-1,-1,-1,-1,0)(1009=-1,-1,-1,-1,0)</Name>
        </SetRegEntry>
        <SetRegEntry>
          <Number>0</Number>
          <Key>ARMDBGFLAGS</Key>
          <Name></Name>
        </SetRegEntry>
        <SetRegEntry>
          <Number>0</Number>
          <Key>DLGUARM</Key>
          <Name></Name>
        </SetRegEntry>
        <SetRegEntry>
          <Number>0</Number>
          <Key>CMSIS_AGDI</Key>
          <Name>-X"Any" -UAny -O718 -S9 -C0 -********* -N00("ARM CoreSight SW-DP") -D00(6BA02477) -L00(0) -TO65554 -********** -********** -TP20 -TDS8007 -TDT0 -TDC1F -TIEFFFFFFFF -TIP8 -FO15 -********** -FC8000 -FN1 -FF0MSPM0G1X0X_G3X0X_MAIN_128KB.FLM -FS00 -FL020000 -FP0($$Device:MSPM0G3507$02_Flash_Programming\FlashARM\MSPM0G1X0X_G3X0X_MAIN_128KB.FLM)</Name>
        </SetRegEntry>
        <SetRegEntry>
          <Number>0</Number>
          <Key>UL2CM3</Key>
          <Name>UL2CM3(-S0 -C0 -P0 -********** -FC8000 -FN1 -FF0MSPM0G_MAIN_128KB -FS00 -FL020000 -FP0($$Device:MSPM0G3507$02_Flash_Programming\FlashARM\MSPM0G_MAIN_128KB.FLM))</Name>
        </SetRegEntry>
      </TargetDriverDllRegistry>
      <Breakpoint/>
      <MemoryWindow1>
        <Mm>
          <WinNumber>1</WinNumber>
          <SubType>0</SubType>
          <ItemText>0x00006000</ItemText>
          <AccSizeX>0</AccSizeX>
        </Mm>
      </MemoryWindow1>
      <Tracepoint>
        <THDelay>0</THDelay>
      </Tracepoint>
      <DebugFlag>
        <trace>0</trace>
        <periodic>1</periodic>
        <aLwin>1</aLwin>
        <aCover>0</aCover>
        <aSer1>0</aSer1>
        <aSer2>0</aSer2>
        <aPa>0</aPa>
        <viewmode>1</viewmode>
        <vrSel>0</vrSel>
        <aSym>0</aSym>
        <aTbox>0</aTbox>
        <AscS1>0</AscS1>
        <AscS2>0</AscS2>
        <AscS3>0</AscS3>
        <aSer3>0</aSer3>
        <eProf>0</eProf>
        <aLa>0</aLa>
        <aPa1>0</aPa1>
        <AscS4>0</AscS4>
        <aSer4>0</aSer4>
        <StkLoc>1</StkLoc>
        <TrcWin>0</TrcWin>
        <newCpu>0</newCpu>
        <uProt>0</uProt>
      </DebugFlag>
      <LintExecutable></LintExecutable>
      <LintConfigFile></LintConfigFile>
      <bLintAuto>0</bLintAuto>
      <bAutoGenD>0</bAutoGenD>
      <LntExFlags>0</LntExFlags>
      <pMisraName></pMisraName>
      <pszMrule></pszMrule>
      <pSingCmds></pSingCmds>
      <pMultCmds></pMultCmds>
      <pMisraNamep></pMisraNamep>
      <pszMrulep></pszMrulep>
      <pSingCmdsp></pSingCmdsp>
      <pMultCmdsp></pMultCmdsp>
      <DebugDescription>
        <Enable>1</Enable>
        <EnableFlashSeq>1</EnableFlashSeq>
        <EnableLog>1</EnableLog>
        <Protocol>2</Protocol>
        <DbgClock>5000000</DbgClock>
      </DebugDescription>
    </TargetOption>
  </Target>

  <Group>
    <GroupName>user</GroupName>
    <tvExp>1</tvExp>
    <tvExpOptDlg>0</tvExpOptDlg>
    <cbSel>0</cbSel>
    <RteFlg>0</RteFlg>
    <File>
      <GroupNumber>1</GroupNumber>
      <FileNumber>1</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\user\src\main.c</PathWithFileName>
      <FilenameWithoutPath>main.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>1</GroupNumber>
      <FileNumber>2</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\user\src\isr.c</PathWithFileName>
      <FilenameWithoutPath>isr.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>1</GroupNumber>
      <FileNumber>3</FileNumber>
      <FileType>5</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\user\inc\isr.h</PathWithFileName>
      <FilenameWithoutPath>isr.h</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
  </Group>

  <Group>
    <GroupName>zf_common</GroupName>
    <tvExp>0</tvExp>
    <tvExpOptDlg>0</tvExpOptDlg>
    <cbSel>0</cbSel>
    <RteFlg>0</RteFlg>
    <File>
      <GroupNumber>2</GroupNumber>
      <FileNumber>4</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\..\libraries\zf_common\zf_common_clock.c</PathWithFileName>
      <FilenameWithoutPath>zf_common_clock.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>2</GroupNumber>
      <FileNumber>5</FileNumber>
      <FileType>5</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\..\libraries\zf_common\zf_common_clock.h</PathWithFileName>
      <FilenameWithoutPath>zf_common_clock.h</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>2</GroupNumber>
      <FileNumber>6</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\..\libraries\zf_common\zf_common_debug.c</PathWithFileName>
      <FilenameWithoutPath>zf_common_debug.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>2</GroupNumber>
      <FileNumber>7</FileNumber>
      <FileType>5</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\..\libraries\zf_common\zf_common_debug.h</PathWithFileName>
      <FilenameWithoutPath>zf_common_debug.h</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>2</GroupNumber>
      <FileNumber>8</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\..\libraries\zf_common\zf_common_fifo.c</PathWithFileName>
      <FilenameWithoutPath>zf_common_fifo.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>2</GroupNumber>
      <FileNumber>9</FileNumber>
      <FileType>5</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\..\libraries\zf_common\zf_common_fifo.h</PathWithFileName>
      <FilenameWithoutPath>zf_common_fifo.h</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>2</GroupNumber>
      <FileNumber>10</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\..\libraries\zf_common\zf_common_font.c</PathWithFileName>
      <FilenameWithoutPath>zf_common_font.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>2</GroupNumber>
      <FileNumber>11</FileNumber>
      <FileType>5</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\..\libraries\zf_common\zf_common_font.h</PathWithFileName>
      <FilenameWithoutPath>zf_common_font.h</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>2</GroupNumber>
      <FileNumber>12</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\..\libraries\zf_common\zf_common_function.c</PathWithFileName>
      <FilenameWithoutPath>zf_common_function.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>2</GroupNumber>
      <FileNumber>13</FileNumber>
      <FileType>5</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\..\libraries\zf_common\zf_common_function.h</PathWithFileName>
      <FilenameWithoutPath>zf_common_function.h</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>2</GroupNumber>
      <FileNumber>14</FileNumber>
      <FileType>5</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\..\libraries\zf_common\zf_common_headfile.h</PathWithFileName>
      <FilenameWithoutPath>zf_common_headfile.h</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>2</GroupNumber>
      <FileNumber>15</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\..\libraries\zf_common\zf_common_interrupt.c</PathWithFileName>
      <FilenameWithoutPath>zf_common_interrupt.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>2</GroupNumber>
      <FileNumber>16</FileNumber>
      <FileType>5</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\..\libraries\zf_common\zf_common_interrupt.h</PathWithFileName>
      <FilenameWithoutPath>zf_common_interrupt.h</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>2</GroupNumber>
      <FileNumber>17</FileNumber>
      <FileType>5</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\..\libraries\zf_common\zf_common_typedef.h</PathWithFileName>
      <FilenameWithoutPath>zf_common_typedef.h</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
  </Group>

  <Group>
    <GroupName>zf_components</GroupName>
    <tvExp>0</tvExp>
    <tvExpOptDlg>0</tvExpOptDlg>
    <cbSel>0</cbSel>
    <RteFlg>0</RteFlg>
    <File>
      <GroupNumber>3</GroupNumber>
      <FileNumber>18</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\..\libraries\zf_components\seekfree_assistant.c</PathWithFileName>
      <FilenameWithoutPath>seekfree_assistant.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>3</GroupNumber>
      <FileNumber>19</FileNumber>
      <FileType>5</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\..\libraries\zf_components\seekfree_assistant.h</PathWithFileName>
      <FilenameWithoutPath>seekfree_assistant.h</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>3</GroupNumber>
      <FileNumber>20</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\..\libraries\zf_components\seekfree_assistant_interface.c</PathWithFileName>
      <FilenameWithoutPath>seekfree_assistant_interface.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>3</GroupNumber>
      <FileNumber>21</FileNumber>
      <FileType>5</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\..\libraries\zf_components\seekfree_assistant_interface.h</PathWithFileName>
      <FilenameWithoutPath>seekfree_assistant_interface.h</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
  </Group>

  <Group>
    <GroupName>zf_driver</GroupName>
    <tvExp>1</tvExp>
    <tvExpOptDlg>0</tvExpOptDlg>
    <cbSel>0</cbSel>
    <RteFlg>0</RteFlg>
    <File>
      <GroupNumber>4</GroupNumber>
      <FileNumber>22</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\..\libraries\zf_driver\zf_driver_adc.c</PathWithFileName>
      <FilenameWithoutPath>zf_driver_adc.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>4</GroupNumber>
      <FileNumber>23</FileNumber>
      <FileType>5</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\..\libraries\zf_driver\zf_driver_adc.h</PathWithFileName>
      <FilenameWithoutPath>zf_driver_adc.h</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>4</GroupNumber>
      <FileNumber>24</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\..\libraries\zf_driver\zf_driver_delay.c</PathWithFileName>
      <FilenameWithoutPath>zf_driver_delay.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>4</GroupNumber>
      <FileNumber>25</FileNumber>
      <FileType>5</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\..\libraries\zf_driver\zf_driver_delay.h</PathWithFileName>
      <FilenameWithoutPath>zf_driver_delay.h</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>4</GroupNumber>
      <FileNumber>26</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\..\libraries\zf_driver\zf_driver_exti.c</PathWithFileName>
      <FilenameWithoutPath>zf_driver_exti.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>4</GroupNumber>
      <FileNumber>27</FileNumber>
      <FileType>5</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\..\libraries\zf_driver\zf_driver_exti.h</PathWithFileName>
      <FilenameWithoutPath>zf_driver_exti.h</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>4</GroupNumber>
      <FileNumber>28</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\..\libraries\zf_driver\zf_driver_gpio.c</PathWithFileName>
      <FilenameWithoutPath>zf_driver_gpio.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>4</GroupNumber>
      <FileNumber>29</FileNumber>
      <FileType>5</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\..\libraries\zf_driver\zf_driver_gpio.h</PathWithFileName>
      <FilenameWithoutPath>zf_driver_gpio.h</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>4</GroupNumber>
      <FileNumber>30</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\..\libraries\zf_driver\zf_driver_pit.c</PathWithFileName>
      <FilenameWithoutPath>zf_driver_pit.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>4</GroupNumber>
      <FileNumber>31</FileNumber>
      <FileType>5</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\..\libraries\zf_driver\zf_driver_pit.h</PathWithFileName>
      <FilenameWithoutPath>zf_driver_pit.h</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>4</GroupNumber>
      <FileNumber>32</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\..\libraries\zf_driver\zf_driver_pwm.c</PathWithFileName>
      <FilenameWithoutPath>zf_driver_pwm.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>4</GroupNumber>
      <FileNumber>33</FileNumber>
      <FileType>5</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\..\libraries\zf_driver\zf_driver_pwm.h</PathWithFileName>
      <FilenameWithoutPath>zf_driver_pwm.h</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>4</GroupNumber>
      <FileNumber>34</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\..\libraries\zf_driver\zf_driver_soft_iic.c</PathWithFileName>
      <FilenameWithoutPath>zf_driver_soft_iic.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>4</GroupNumber>
      <FileNumber>35</FileNumber>
      <FileType>5</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\..\libraries\zf_driver\zf_driver_soft_iic.h</PathWithFileName>
      <FilenameWithoutPath>zf_driver_soft_iic.h</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>4</GroupNumber>
      <FileNumber>36</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\..\libraries\zf_driver\zf_driver_spi.c</PathWithFileName>
      <FilenameWithoutPath>zf_driver_spi.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>4</GroupNumber>
      <FileNumber>37</FileNumber>
      <FileType>5</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\..\libraries\zf_driver\zf_driver_spi.h</PathWithFileName>
      <FilenameWithoutPath>zf_driver_spi.h</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>4</GroupNumber>
      <FileNumber>38</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\..\libraries\zf_driver\zf_driver_timer.c</PathWithFileName>
      <FilenameWithoutPath>zf_driver_timer.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>4</GroupNumber>
      <FileNumber>39</FileNumber>
      <FileType>5</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\..\libraries\zf_driver\zf_driver_timer.h</PathWithFileName>
      <FilenameWithoutPath>zf_driver_timer.h</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>4</GroupNumber>
      <FileNumber>40</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\..\libraries\zf_driver\zf_driver_uart.c</PathWithFileName>
      <FilenameWithoutPath>zf_driver_uart.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>4</GroupNumber>
      <FileNumber>41</FileNumber>
      <FileType>5</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\..\libraries\zf_driver\zf_driver_uart.h</PathWithFileName>
      <FilenameWithoutPath>zf_driver_uart.h</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>4</GroupNumber>
      <FileNumber>42</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\..\libraries\zf_driver\zf_driver_flash.c</PathWithFileName>
      <FilenameWithoutPath>zf_driver_flash.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>4</GroupNumber>
      <FileNumber>43</FileNumber>
      <FileType>5</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\..\libraries\zf_driver\zf_driver_flash.h</PathWithFileName>
      <FilenameWithoutPath>zf_driver_flash.h</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
  </Group>

  <Group>
    <GroupName>zf_device</GroupName>
    <tvExp>1</tvExp>
    <tvExpOptDlg>0</tvExpOptDlg>
    <cbSel>0</cbSel>
    <RteFlg>0</RteFlg>
    <File>
      <GroupNumber>5</GroupNumber>
      <FileNumber>44</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\..\libraries\zf_device\zf_device_absolute_encoder.c</PathWithFileName>
      <FilenameWithoutPath>zf_device_absolute_encoder.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>5</GroupNumber>
      <FileNumber>45</FileNumber>
      <FileType>5</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\..\libraries\zf_device\zf_device_absolute_encoder.h</PathWithFileName>
      <FilenameWithoutPath>zf_device_absolute_encoder.h</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>5</GroupNumber>
      <FileNumber>46</FileNumber>
      <FileType>5</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\..\libraries\zf_device\zf_device_config.h</PathWithFileName>
      <FilenameWithoutPath>zf_device_config.h</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>5</GroupNumber>
      <FileNumber>47</FileNumber>
      <FileType>4</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\..\libraries\zf_device\zf_device_config.lib</PathWithFileName>
      <FilenameWithoutPath>zf_device_config.lib</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>5</GroupNumber>
      <FileNumber>48</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\..\libraries\zf_device\zf_device_dl1a.c</PathWithFileName>
      <FilenameWithoutPath>zf_device_dl1a.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>5</GroupNumber>
      <FileNumber>49</FileNumber>
      <FileType>5</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\..\libraries\zf_device\zf_device_dl1a.h</PathWithFileName>
      <FilenameWithoutPath>zf_device_dl1a.h</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>5</GroupNumber>
      <FileNumber>50</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\..\libraries\zf_device\zf_device_dl1b.c</PathWithFileName>
      <FilenameWithoutPath>zf_device_dl1b.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>5</GroupNumber>
      <FileNumber>51</FileNumber>
      <FileType>5</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\..\libraries\zf_device\zf_device_dl1b.h</PathWithFileName>
      <FilenameWithoutPath>zf_device_dl1b.h</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>5</GroupNumber>
      <FileNumber>52</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\..\libraries\zf_device\zf_device_imu660ra.c</PathWithFileName>
      <FilenameWithoutPath>zf_device_imu660ra.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>5</GroupNumber>
      <FileNumber>53</FileNumber>
      <FileType>5</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\..\libraries\zf_device\zf_device_imu660ra.h</PathWithFileName>
      <FilenameWithoutPath>zf_device_imu660ra.h</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>5</GroupNumber>
      <FileNumber>54</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\..\libraries\zf_device\zf_device_imu660rb.c</PathWithFileName>
      <FilenameWithoutPath>zf_device_imu660rb.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>5</GroupNumber>
      <FileNumber>55</FileNumber>
      <FileType>5</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\..\libraries\zf_device\zf_device_imu660rb.h</PathWithFileName>
      <FilenameWithoutPath>zf_device_imu660rb.h</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>5</GroupNumber>
      <FileNumber>56</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\..\libraries\zf_device\zf_device_imu963ra.c</PathWithFileName>
      <FilenameWithoutPath>zf_device_imu963ra.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>5</GroupNumber>
      <FileNumber>57</FileNumber>
      <FileType>5</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\..\libraries\zf_device\zf_device_imu963ra.h</PathWithFileName>
      <FilenameWithoutPath>zf_device_imu963ra.h</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>5</GroupNumber>
      <FileNumber>58</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\..\libraries\zf_device\zf_device_ips114.c</PathWithFileName>
      <FilenameWithoutPath>zf_device_ips114.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>5</GroupNumber>
      <FileNumber>59</FileNumber>
      <FileType>5</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\..\libraries\zf_device\zf_device_ips114.h</PathWithFileName>
      <FilenameWithoutPath>zf_device_ips114.h</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>5</GroupNumber>
      <FileNumber>60</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\..\libraries\zf_device\zf_device_ips200.c</PathWithFileName>
      <FilenameWithoutPath>zf_device_ips200.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>5</GroupNumber>
      <FileNumber>61</FileNumber>
      <FileType>5</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\..\libraries\zf_device\zf_device_ips200.h</PathWithFileName>
      <FilenameWithoutPath>zf_device_ips200.h</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>5</GroupNumber>
      <FileNumber>62</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\..\libraries\zf_device\zf_device_ips200pro.c</PathWithFileName>
      <FilenameWithoutPath>zf_device_ips200pro.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>5</GroupNumber>
      <FileNumber>63</FileNumber>
      <FileType>5</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\..\libraries\zf_device\zf_device_ips200pro.h</PathWithFileName>
      <FilenameWithoutPath>zf_device_ips200pro.h</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>5</GroupNumber>
      <FileNumber>64</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\..\libraries\zf_device\zf_device_tft180.c</PathWithFileName>
      <FilenameWithoutPath>zf_device_tft180.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>5</GroupNumber>
      <FileNumber>65</FileNumber>
      <FileType>5</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\..\libraries\zf_device\zf_device_tft180.h</PathWithFileName>
      <FilenameWithoutPath>zf_device_tft180.h</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>5</GroupNumber>
      <FileNumber>66</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\..\libraries\zf_device\zf_device_tsl1401.c</PathWithFileName>
      <FilenameWithoutPath>zf_device_tsl1401.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>5</GroupNumber>
      <FileNumber>67</FileNumber>
      <FileType>5</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\..\libraries\zf_device\zf_device_tsl1401.h</PathWithFileName>
      <FilenameWithoutPath>zf_device_tsl1401.h</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>5</GroupNumber>
      <FileNumber>68</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\..\libraries\zf_device\zf_device_type.c</PathWithFileName>
      <FilenameWithoutPath>zf_device_type.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>5</GroupNumber>
      <FileNumber>69</FileNumber>
      <FileType>5</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\..\libraries\zf_device\zf_device_type.h</PathWithFileName>
      <FilenameWithoutPath>zf_device_type.h</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>5</GroupNumber>
      <FileNumber>70</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\..\libraries\zf_device\zf_device_wifi_spi.c</PathWithFileName>
      <FilenameWithoutPath>zf_device_wifi_spi.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>5</GroupNumber>
      <FileNumber>71</FileNumber>
      <FileType>5</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\..\libraries\zf_device\zf_device_wifi_spi.h</PathWithFileName>
      <FilenameWithoutPath>zf_device_wifi_spi.h</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>5</GroupNumber>
      <FileNumber>72</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\..\libraries\zf_device\zf_device_wifi_uart.c</PathWithFileName>
      <FilenameWithoutPath>zf_device_wifi_uart.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>5</GroupNumber>
      <FileNumber>73</FileNumber>
      <FileType>5</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\..\libraries\zf_device\zf_device_wifi_uart.h</PathWithFileName>
      <FilenameWithoutPath>zf_device_wifi_uart.h</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>5</GroupNumber>
      <FileNumber>74</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\..\libraries\zf_device\zf_device_wireless_uart.c</PathWithFileName>
      <FilenameWithoutPath>zf_device_wireless_uart.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>5</GroupNumber>
      <FileNumber>75</FileNumber>
      <FileType>5</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\..\libraries\zf_device\zf_device_wireless_uart.h</PathWithFileName>
      <FilenameWithoutPath>zf_device_wireless_uart.h</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>5</GroupNumber>
      <FileNumber>76</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\..\libraries\zf_device\zf_device_key.c</PathWithFileName>
      <FilenameWithoutPath>zf_device_key.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>5</GroupNumber>
      <FileNumber>77</FileNumber>
      <FileType>5</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\..\libraries\zf_device\zf_device_key.h</PathWithFileName>
      <FilenameWithoutPath>zf_device_key.h</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
  </Group>

  <Group>
    <GroupName>code</GroupName>
    <tvExp>0</tvExp>
    <tvExpOptDlg>0</tvExpOptDlg>
    <cbSel>0</cbSel>
    <RteFlg>0</RteFlg>
  </Group>

  <Group>
    <GroupName>ti_config</GroupName>
    <tvExp>1</tvExp>
    <tvExpOptDlg>0</tvExpOptDlg>
    <cbSel>0</cbSel>
    <RteFlg>0</RteFlg>
    <File>
      <GroupNumber>7</GroupNumber>
      <FileNumber>78</FileNumber>
      <FileType>2</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\..\libraries\sdk\ti_config\startup_mspm0g350x_uvision.s</PathWithFileName>
      <FilenameWithoutPath>startup_mspm0g350x_uvision.s</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>7</GroupNumber>
      <FileNumber>79</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\..\libraries\sdk\ti_config\ti_msp_dl_config.c</PathWithFileName>
      <FilenameWithoutPath>ti_msp_dl_config.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>7</GroupNumber>
      <FileNumber>80</FileNumber>
      <FileType>5</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\..\libraries\sdk\ti_config\ti_msp_dl_config.h</PathWithFileName>
      <FilenameWithoutPath>ti_msp_dl_config.h</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
  </Group>

  <Group>
    <GroupName>Driverlib</GroupName>
    <tvExp>0</tvExp>
    <tvExpOptDlg>0</tvExpOptDlg>
    <cbSel>0</cbSel>
    <RteFlg>0</RteFlg>
    <File>
      <GroupNumber>8</GroupNumber>
      <FileNumber>81</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\..\libraries\sdk\ti\driverlib\dl_adc12.c</PathWithFileName>
      <FilenameWithoutPath>dl_adc12.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>8</GroupNumber>
      <FileNumber>82</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\..\libraries\sdk\ti\driverlib\dl_aes.c</PathWithFileName>
      <FilenameWithoutPath>dl_aes.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>8</GroupNumber>
      <FileNumber>83</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\..\libraries\sdk\ti\driverlib\dl_aesadv.c</PathWithFileName>
      <FilenameWithoutPath>dl_aesadv.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>8</GroupNumber>
      <FileNumber>84</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\..\libraries\sdk\ti\driverlib\dl_common.c</PathWithFileName>
      <FilenameWithoutPath>dl_common.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>8</GroupNumber>
      <FileNumber>85</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\..\libraries\sdk\ti\driverlib\dl_comp.c</PathWithFileName>
      <FilenameWithoutPath>dl_comp.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>8</GroupNumber>
      <FileNumber>86</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\..\libraries\sdk\ti\driverlib\dl_crc.c</PathWithFileName>
      <FilenameWithoutPath>dl_crc.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>8</GroupNumber>
      <FileNumber>87</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\..\libraries\sdk\ti\driverlib\dl_crcp.c</PathWithFileName>
      <FilenameWithoutPath>dl_crcp.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>8</GroupNumber>
      <FileNumber>88</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\..\libraries\sdk\ti\driverlib\dl_dac12.c</PathWithFileName>
      <FilenameWithoutPath>dl_dac12.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>8</GroupNumber>
      <FileNumber>89</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\..\libraries\sdk\ti\driverlib\dl_dma.c</PathWithFileName>
      <FilenameWithoutPath>dl_dma.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>8</GroupNumber>
      <FileNumber>90</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\..\libraries\sdk\ti\driverlib\dl_flashctl.c</PathWithFileName>
      <FilenameWithoutPath>dl_flashctl.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>8</GroupNumber>
      <FileNumber>91</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\..\libraries\sdk\ti\driverlib\dl_gpamp.c</PathWithFileName>
      <FilenameWithoutPath>dl_gpamp.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>8</GroupNumber>
      <FileNumber>92</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\..\libraries\sdk\ti\driverlib\dl_gpio.c</PathWithFileName>
      <FilenameWithoutPath>dl_gpio.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>8</GroupNumber>
      <FileNumber>93</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\..\libraries\sdk\ti\driverlib\dl_i2c.c</PathWithFileName>
      <FilenameWithoutPath>dl_i2c.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>8</GroupNumber>
      <FileNumber>94</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\..\libraries\sdk\ti\driverlib\dl_iwdt.c</PathWithFileName>
      <FilenameWithoutPath>dl_iwdt.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>8</GroupNumber>
      <FileNumber>95</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\..\libraries\sdk\ti\driverlib\dl_keystorectl.c</PathWithFileName>
      <FilenameWithoutPath>dl_keystorectl.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>8</GroupNumber>
      <FileNumber>96</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\..\libraries\sdk\ti\driverlib\dl_lcd.c</PathWithFileName>
      <FilenameWithoutPath>dl_lcd.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>8</GroupNumber>
      <FileNumber>97</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\..\libraries\sdk\ti\driverlib\dl_lfss.c</PathWithFileName>
      <FilenameWithoutPath>dl_lfss.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>8</GroupNumber>
      <FileNumber>98</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\..\libraries\sdk\ti\driverlib\dl_mathacl.c</PathWithFileName>
      <FilenameWithoutPath>dl_mathacl.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>8</GroupNumber>
      <FileNumber>99</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\..\libraries\sdk\ti\driverlib\dl_mcan.c</PathWithFileName>
      <FilenameWithoutPath>dl_mcan.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>8</GroupNumber>
      <FileNumber>100</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\..\libraries\sdk\ti\driverlib\dl_opa.c</PathWithFileName>
      <FilenameWithoutPath>dl_opa.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>8</GroupNumber>
      <FileNumber>101</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\..\libraries\sdk\ti\driverlib\dl_rtc.c</PathWithFileName>
      <FilenameWithoutPath>dl_rtc.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>8</GroupNumber>
      <FileNumber>102</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\..\libraries\sdk\ti\driverlib\dl_rtc_a.c</PathWithFileName>
      <FilenameWithoutPath>dl_rtc_a.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>8</GroupNumber>
      <FileNumber>103</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\..\libraries\sdk\ti\driverlib\dl_rtc_b.c</PathWithFileName>
      <FilenameWithoutPath>dl_rtc_b.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>8</GroupNumber>
      <FileNumber>104</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\..\libraries\sdk\ti\driverlib\dl_rtc_common.c</PathWithFileName>
      <FilenameWithoutPath>dl_rtc_common.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>8</GroupNumber>
      <FileNumber>105</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\..\libraries\sdk\ti\driverlib\dl_scratchpad.c</PathWithFileName>
      <FilenameWithoutPath>dl_scratchpad.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>8</GroupNumber>
      <FileNumber>106</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\..\libraries\sdk\ti\driverlib\dl_spi.c</PathWithFileName>
      <FilenameWithoutPath>dl_spi.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>8</GroupNumber>
      <FileNumber>107</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\..\libraries\sdk\ti\driverlib\dl_tamperio.c</PathWithFileName>
      <FilenameWithoutPath>dl_tamperio.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>8</GroupNumber>
      <FileNumber>108</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\..\libraries\sdk\ti\driverlib\dl_timer.c</PathWithFileName>
      <FilenameWithoutPath>dl_timer.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>8</GroupNumber>
      <FileNumber>109</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\..\libraries\sdk\ti\driverlib\dl_timera.c</PathWithFileName>
      <FilenameWithoutPath>dl_timera.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>8</GroupNumber>
      <FileNumber>110</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\..\libraries\sdk\ti\driverlib\dl_timerg.c</PathWithFileName>
      <FilenameWithoutPath>dl_timerg.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>8</GroupNumber>
      <FileNumber>111</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\..\libraries\sdk\ti\driverlib\dl_trng.c</PathWithFileName>
      <FilenameWithoutPath>dl_trng.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>8</GroupNumber>
      <FileNumber>112</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\..\libraries\sdk\ti\driverlib\dl_uart.c</PathWithFileName>
      <FilenameWithoutPath>dl_uart.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>8</GroupNumber>
      <FileNumber>113</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\..\libraries\sdk\ti\driverlib\dl_vref.c</PathWithFileName>
      <FilenameWithoutPath>dl_vref.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>8</GroupNumber>
      <FileNumber>114</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\..\libraries\sdk\ti\driverlib\dl_wwdt.c</PathWithFileName>
      <FilenameWithoutPath>dl_wwdt.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
  </Group>

</ProjectOpt>
