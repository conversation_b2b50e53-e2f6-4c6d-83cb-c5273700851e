; *************************************************************
; *** Scatter-Loading Description File generated by uVision ***
; *************************************************************

LR_IROM1 0x00000000 0x00020000  {    ; load region size_region
  ER_IROM1 0x00000000 ALIGNALL 8 0x00020000  {  ; load address = execution address
   *.o (RESET, +First)
   *(InRoot$$Sections)
   .ANY (+RO)
   .ANY (+XO)
  }
  RW_IRAM2 0x20200000 0x00008000  {  ; RW data
   .ANY (+RW +ZI)
   .ANY (.ramfunc)
  }
}

LR_BCR 0x41C00000 0x00000100  {    ; load region size_region
  BCR_CONFIG 0x41C00000 0x00000080{
	.ANY (.BCRConfig)
  }
}

LR_BSL 0x41C00100 0x00000100  {    ; load region size_region
   BSL_CONFIG 0x41C00100 0x00000080{
	.ANY (.BSLConfig)
  }
}
