# 感为8路灰度传感器集成到逐飞库说明

## 📋 集成概述

本文档说明如何将感为无MCU8路灰度传感器代码集成到逐飞MSPM0G3507开源库中，实现传感器功能的模块化和标准化。

## 🔄 集成步骤

### 1. 代码结构分析

感为传感器原始代码结构：
```
gpio_toggle_output/
├── keil/User/
│   ├── No_Mcu_Ganv_Grayscale_Sensor.c      # 传感器核心算法
│   └── No_Mcu_Ganv_Grayscale_Sensor_Config.h # 传感器配置头文件
├── keil/BSP/
│   ├── ADC.c/.h                             # ADC驱动
│   ├── Time.c/.h                            # 延时函数
│   └── Uart.c/.h                            # 串口驱动
└── gpio_toggle_output.c                     # 主程序示例
```

### 2. 逐飞库集成方案

#### 2.1 设备层集成
在逐飞库的设备层(`zf_device`)中创建感为传感器驱动：

**新增文件：**
- `zf_device_ganwei_grayscale.h` - 传感器设备头文件
- `zf_device_ganwei_grayscale.c` - 传感器设备实现文件

#### 2.2 硬件抽象层适配
将感为传感器的硬件操作适配到逐飞库的驱动接口：

| 感为原始接口 | 逐飞库接口 | 说明 |
|-------------|-----------|------|
| `Switch_Address_X()` | `gpio_set_level()` | GPIO控制地址线 |
| `Get_adc_of_user()` | `adc_convert()` | ADC数据采集 |
| `delay_ms()` | `system_delay_ms()` | 延时函数 |

#### 2.3 数据结构重构
将感为传感器的数据结构适配到逐飞库的命名规范：

```c
// 感为原始结构体
typedef struct {
    unsigned short Analog_value[8];
    unsigned short Normal_value[8];
    // ...
} No_MCU_Sensor;

// 逐飞库适配结构体
typedef struct {
    uint16 analog_value[8];
    uint16 normal_value[8];
    // ...
} ganwei_grayscale_info_struct;
```

## 📁 集成后的文件结构

```
SeekFree_MSPM0G3507_Opensource_Library/
├── libraries/zf_device/
│   ├── zf_device_ganwei_grayscale.h        # 感为传感器设备头文件
│   ├── zf_device_ganwei_grayscale.c        # 感为传感器设备实现
│   └── ...
└── Example/Motherboard_Demo/
    ├── E9_ganwei_grayscale/                 # 感为传感器例程
    │   └── user/src/main.c                  # 使用示例
    └── ...
```

## 🔧 硬件连接说明

### 引脚连接
| 传感器引脚 | MSPM0G3507引脚 | 功能说明 |
|-----------|---------------|----------|
| 地址线0 | A0 (PA0) | 通道选择地址位0 |
| 地址线1 | A1 (PA1) | 通道选择地址位1 |
| 地址线2 | A2 (PA2) | 通道选择地址位2 |
| 模拟输出 | A27 (PA27) | ADC0_CH0采集引脚 |
| VCC | 3.3V | 电源正极 |
| GND | GND | 电源负极 |

### 传感器版本支持
- **经典版传感器**：理论性能1kHz，延时1ms
- **青春版传感器**：理论性能100Hz，延时10ms

## 💻 使用方法

### 1. 基础初始化
```c
#include "zf_device_ganwei_grayscale.h"

ganwei_grayscale_info_struct grayscale_sensor;

// 初始化传感器
ganwei_grayscale_init(&grayscale_sensor,
                     GANWEI_GRAYSCALE_CLASS_EDITION,    // 传感器版本
                     GANWEI_GRAYSCALE_ADC_12BITS,       // ADC分辨率
                     A0, A1, A2,                        // 地址线引脚
                     ADC0_CH0_A27);                     // ADC引脚
```

### 2. 校准设置
```c
// 校准数据（根据实际测试修改）
uint16 white_values[8] = {1800, 1800, 1800, 1800, 1800, 1800, 1800, 1800};
uint16 black_values[8] = {300, 300, 300, 300, 300, 300, 300, 300};

// 加载校准数据
ganwei_grayscale_init_with_calibration(&grayscale_sensor, white_values, black_values);
```

### 3. 数据采集
```c
uint16 analog_data[8];      // 模拟量数据
uint16 normalized_data[8];  // 归一化数据
uint8 digital_data;         // 数字量数据

while(1)
{
    // 执行传感器任务
    ganwei_grayscale_task(&grayscale_sensor);
    
    // 获取各种数据
    digital_data = ganwei_grayscale_get_digital(&grayscale_sensor);
    ganwei_grayscale_get_analog(&grayscale_sensor, analog_data);
    ganwei_grayscale_get_normalized(&grayscale_sensor, normalized_data);
    
    system_delay_ms(1);  // 经典版1ms，青春版10ms
}
```

## 🎯 API接口说明

### 初始化函数
- `ganwei_grayscale_init()` - 基础初始化
- `ganwei_grayscale_init_with_calibration()` - 带校准参数初始化

### 数据获取函数
- `ganwei_grayscale_get_digital()` - 获取8位数字量
- `ganwei_grayscale_get_analog()` - 获取原始模拟量
- `ganwei_grayscale_get_normalized()` - 获取归一化数据

### 任务处理函数
- `ganwei_grayscale_task()` - 主任务处理（需定期调用）

### 配置函数
- `ganwei_grayscale_set_direction()` - 设置输出方向

## ⚙️ 配置选项

### 传感器版本
```c
typedef enum
{
    GANWEI_GRAYSCALE_CLASS_EDITION = 0,  // 经典版
    GANWEI_GRAYSCALE_YOUTH_EDITION = 1,  // 青春版
}ganwei_grayscale_edition_enum;
```

### ADC分辨率
```c
typedef enum
{
    GANWEI_GRAYSCALE_ADC_8BITS  = 0,     // 8位ADC
    GANWEI_GRAYSCALE_ADC_10BITS = 1,     // 10位ADC
    GANWEI_GRAYSCALE_ADC_12BITS = 2,     // 12位ADC
    GANWEI_GRAYSCALE_ADC_14BITS = 3,     // 14位ADC
}ganwei_grayscale_adc_bits_enum;
```

## 🔍 校准方法

### 1. 获取原始ADC值
运行例程，将传感器分别放在白色和黑色表面，记录ADC值。

### 2. 设置校准参数
```c
// 白色表面的ADC值
uint16 white_values[8] = {实际测得的白色ADC值};

// 黑色表面的ADC值  
uint16 black_values[8] = {实际测得的黑色ADC值};
```

### 3. 验证校准效果
- 数字量：0表示黑色，1表示白色
- 归一化值：0-4095范围内的连续值
- 模拟量：原始ADC采集值

## ⚠️ 注意事项

1. **延时设置**：经典版使用1ms延时，青春版使用10ms延时
2. **引脚配置**：确保地址线引脚配置为推挽输出
3. **ADC配置**：确保ADC通道正确初始化
4. **校准数据**：必须先获取实际的黑白校准值
5. **电源供电**：传感器需要稳定的3.3V供电

## 🚀 集成优势

1. **标准化接口**：符合逐飞库的设备层规范
2. **模块化设计**：便于在不同项目中复用
3. **配置灵活**：支持不同版本和分辨率配置
4. **易于使用**：提供完整的使用示例
5. **兼容性好**：完全兼容逐飞库的开发环境

## 📚 相关文档

- [逐飞MSPM0G3507开源库使用说明](./MSPM0G3507学习指南.md)
- [感为传感器原始文档](./gpio_toggle_output/README.md)
- [MSPM0G3507数据手册](./【文档】说明书 芯片手册等/)

---

**集成完成！现在您可以在逐飞库中方便地使用感为8路灰度传感器了。**